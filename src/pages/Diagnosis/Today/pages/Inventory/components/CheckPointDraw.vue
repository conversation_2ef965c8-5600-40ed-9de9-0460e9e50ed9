<template>
  <div class="check-draw-container">
    <div class="draw-filter">
      <CapsuleTab
        v-model="view"
        :options="DrawViewOptions"
        size="small"
        :equal="false"
        bisector
      />
      <CustomSelect v-model="week" :options="weekOptions" size="small">
        <template #prefix>􀉉</template>
      </CustomSelect>
      <CustomSelect v-model="rtm" :options="rtmOptions" size="small">
        <template #prefix>RTM: </template>
      </CustomSelect>
      <CustomSelect v-model="lob" :options="lobOptions" size="small">
        <template #prefix>LOB: </template>
      </CustomSelect>
    </div>
    <el-table :data="data" style="width: 100%">
      <el-table-column
        v-for="column in columns"
        :key="column.prop"
        v-bind="column"
      >
        <!-- <template #default="{ row }">{{ row[value] }}</template> -->
      </el-table-column>
    </el-table>
  </div>
</template>

<script>
import CapsuleTab from "@/components/CapsuleTab";
import CustomSelect from "@/components/CustomSelect";
import { DrawViewOptions } from "../constant";
import { formatCheckPointData } from "../format";
import { CheckData } from "../mock";

export default {
  components: {
    CapsuleTab,
    CustomSelect,
  },
  data() {
    return {
      view: DrawViewOptions?.[0]?.value ?? "",
      DrawViewOptions,
      week: "",
      weekOptions: [],
      rtm: "",
      rtmOptions: [],
      lob: "",
      lobOptions: [],

      data: [],
      columns: [],
    };
  },
  mounted() {
    this.getData();
  },
  methods: {
    getData() {
      const { code = 0, data = {} } = CheckData;
      if (code === 0) {
        const newResult = formatCheckPointData(data);
        this.data = newResult?.data ?? [];
        this.columns = newResult?.columns ?? [];
      }
    },
  },
};
</script>

<style lang="scss" scoped>
.check-draw-container {
  .draw-filter {
    display: grid;
    gap: 16px;
    grid-template-columns: auto 1fr 1fr 1fr;
    margin: 20px 0;
  }
}
</style>
