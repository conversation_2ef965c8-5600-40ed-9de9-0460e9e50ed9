// 优化后的 axios 配置
import { clone } from "lodash-es";
import { VAxios } from "./Axios";
import { RequestEnum, ContentTypeEnum } from "./httpEnum";
import { isString, deepMerge } from "./utils";
import qs from "qs";
import { joinTimestamp, formatRequestDate, formatRestful } from "./helper";
import { AxiosRetry } from "./axiosRetry";
import axios from "axios";

/**
 * 优化后的请求前置钩子
 */
const optimizedBeforeRequestHook = (config, options) => {
  // 1. 处理 URL 前缀
  processUrlPrefix(config, options);

  // 2. 根据请求方法分别处理
  const isGetRequest = config.method?.toUpperCase() === RequestEnum.GET;

  if (isGetRequest) {
    handleGetRequest(config, options);
  } else {
    handleNonGetRequest(config, options);
  }

  return config;
};

/**
 * 处理 URL 前缀拼接
 */
function processUrlPrefix(config, options) {
  const { apiUrl, joinPrefix, urlPrefix } = options;

  if (joinPrefix && urlPrefix && isString(urlPrefix)) {
    config.url = `${urlPrefix}${config.url}`;
  }

  if (apiUrl && isString(apiUrl)) {
    config.url = `${apiUrl}${config.url}`;
  }
}

/**
 * 处理 GET 请求
 */
function handleGetRequest(config, options) {
  const { joinTime = true } = options;
  const params = config.params || {};

  if (isString(params)) {
    // RESTful 风格
    formatRestful(config, params, joinTime);
  } else {
    // 标准查询参数
    config.params = Object.assign(params, joinTimestamp(joinTime, false));
    // 采用qs处理get请求参数，解决axios get请求参数数组问题
    config.paramsSerializer = (params) =>
      qs.stringify(params, { arrayFormat: "repeat" });
  }
}

/**
 * 处理非 GET 请求
 */
function handleNonGetRequest(config, options) {
  const { formatDate, joinParamsToUrl } = options;
  const params = config.params || {};
  const data = config.data || false;

  // 格式化日期
  if (formatDate && data && !isString(data)) {
    formatRequestDate(data);
  }

  if (isString(params)) {
    // RESTful 风格
    formatRestful(config, params, false); // 非GET请求不需要时间戳
  } else {
    // 标准请求体
    if (formatDate) {
      formatRequestDate(params);
    }

    config.data = data;
    config.params = params;

    // 是否将参数拼接到 URL
    if (joinParamsToUrl && params && Object.keys(params).length > 0) {
      const queryString = qs.stringify(params);
      config.url = `${config.url}${
        config.url.includes("?") ? "&" : "?"
      }${queryString}`;
      config.params = undefined;
    }
  }
}

/**
 * @description: 数据处理，方便区分多种处理方式
 */
const transform = {
  /**
   * @description: 处理响应数据。如果数据不是预期格式，可直接抛出错误
   */
  transformResponseHook: (res, options) => {
    const { request = {} } = res;
    const { isTransformResponse, isReturnNativeResponse } = options;

    // 是否返回原生响应头 比如：需要获取响应头时使用该属性
    if (isReturnNativeResponse) {
      return res;
    }
    // 不进行任何处理，直接返回
    // 用于页面代码可能需要直接获取code，data，message这些信息时开启
    if (!isTransformResponse) {
      return res.data;
    }

    return request?.responseType === "blob" ? res : res.data;
  },

  // 使用优化后的请求前置钩子
  beforeRequestHook: optimizedBeforeRequestHook,

  /**
   * @description: 请求拦截器处理
   */
  requestInterceptors: (config, options) => {
    // 请求之前处理config
    const token = getAuthToken();
    if (token && config?.requestOptions?.withToken !== false) {
      // jwt token
      config.headers.Authorization = options.authenticationScheme
        ? `${options.authenticationScheme} ${token}`
        : token;
    }
    return config;
  },

  /**
   * @description: 响应拦截器处理
   */
  responseInterceptors: (res) => {
    return res;
  },

  /**
   * @description: 响应错误处理
   */
  responseInterceptorsCatch: (axiosInstance, error) => {
    const { response, code, message, config } = error || {};

    const msg = response?.data?.message ?? "";
    const err = error?.toString?.() ?? "";
    let errMessage = msg;

    if (axios.isCancel(error)) {
      return Promise.reject(error);
    }

    try {
      if (code === "ECONNABORTED" && message.indexOf("timeout") !== -1) {
        errMessage = "Request timeout, please try again later.";
      }
      if (err?.includes("Network Error")) {
        errMessage = `Network Error: ${err}`;
      }

      if (errMessage) {
        console.error("Request Error:", errMessage);
        return Promise.reject(error);
      }
    } catch (error) {
      console.error("Error processing failed request:", error);
      return Promise.reject(error);
    }

    // 添加自动重试机制 保险起见 只针对GET请求
    const retryRequest = new AxiosRetry();
    const { isOpenRetry } = config?.requestOptions?.retryRequest || {};
    if (config.method?.toUpperCase() === RequestEnum.GET && isOpenRetry) {
      retryRequest.retry(axiosInstance, error);
    }
    return Promise.reject(error);
  },
};

/**
 * 获取认证 token
 */
function getAuthToken() {
  try {
    return localStorage.getItem("token");
  } catch (error) {
    console.warn("Failed to get auth token from localStorage:", error);
    return null;
  }
}

function createAxios(opt) {
  return new VAxios(
    // 深度合并
    deepMerge(
      {
        // See https://developer.mozilla.org/en-US/docs/Web/HTTP/Authentication#authentication_schemes
        // authentication schemes，e.g: Bearer
        authenticationScheme: "Bearer",
        timeout: 60 * 1000,
        // 基础接口地址
        baseURL: "",

        headers: { "Content-Type": ContentTypeEnum.JSON },
        // 数据处理方式
        transform: clone(transform),
        // 配置项，下面的选项都可以在独立的接口请求中覆盖
        requestOptions: {
          // 默认将prefix 添加到url
          joinPrefix: true,
          // 是否返回原生响应头 比如：需要获取响应头时使用该属性
          isReturnNativeResponse: false,
          // 需要对返回数据进行处理
          isTransformResponse: true,
          // post请求的时候添加参数到url
          joinParamsToUrl: false,
          // 格式化提交参数时间
          formatDate: true,
          // 消息提示类型
          errorMessageMode: "message",
          // 接口地址
          apiUrl: "",
          // 接口拼接地址
          urlPrefix: "",
          //  是否加入时间戳
          joinTime: true,
          // 忽略重复请求
          ignoreCancelToken: true,
          // 是否携带token
          withToken: false,
          retryRequest: {
            isOpenRetry: false,
            count: 5,
            waitTime: 100,
          },
          otherCode: [],
        },
      },
      opt || {}
    )
  );
}

const http = createAxios();
export { createAxios, http };
