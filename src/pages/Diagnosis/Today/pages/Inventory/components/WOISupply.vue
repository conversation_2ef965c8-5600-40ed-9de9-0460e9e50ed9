<template>
  <div
    v-log.module.visible="{
      event_name: 'smart_tower_today_Inventory_inwoiinv_expose',
    }"
    v-log:5.stay="{
      event_name: 'smart_tower_today_Inventory_inwoiinv_stay',
    }"
  >
    <div class="header">
      <div class="panel-title">{{ lob }} {{ currentSupply }}</div>
    </div>
    <el-table
      v-load="loading"
      v-eltable-empty="tableData"
      :cell-class-name="tableCellClassName"
      :data="tableData"
    >
      <el-table-column
        v-for="(item, index) in columns"
        v-bind="item"
        :key="item.prop + index"
        :width="item.width * zoomValue"
      >
        <!-- eslint-disable-next-line -->
        <template slot="header" slot-scope="scope">
          <template
            v-if="
              performance === 'in_stock' &&
              ['JD Self-run', 'Total'].includes(item.label)
            "
          >
            <span class="prompt-wrapper">
              {{ item.label }}
              <PromptTooltip
                :popper-class="`smart_tower_tool_tip_${item.label}`"
                :content="item.label | promptContent"
              />
            </span>
          </template>
          <template v-else>
            <span>{{ item.label }}</span>
          </template>
        </template>
        <template slot-scope="scope">
          <span v-if="item.prop === 'Sub-LOB'" class="break-word">
            {{ scope.row[item.prop] }}
          </span>
          <span
            v-else
            :class="calcNumHighlight(scope.row[item.prop], item.prop)"
          >
            {{ scope.row[item.prop][performance] }}
          </span>
        </template>
      </el-table-column>
      <template slot="empty">
        <TableNoData :templateType="loading ? '' : 'noData'" />
      </template>
    </el-table>
  </div>
</template>

<script>
import { isNumber } from "lodash-es";
import TableNoData from "@/pages/Diagnosis/components/TableNoData.vue";
import diagnosisInventoryService from "@/services/DiagnosisInventoryService";
import PromptTooltip from "./PromptTooltip.vue";
import { supplyOptions } from "../constant";
import {
  formatSummaryDetailColumns,
  formatSummaryDetailData,
  getNumberWithTwoDecimals,
} from "../format";
import { sendLog } from "@/utils/sendLog";

export default {
  props: {
    lob: {
      type: String,
      default: "",
    },
    hasLob: {
      type: Boolean,
      default: false,
    },
    performance: {
      type: String,
      default: "",
    },
  },
  components: {
    TableNoData,
    PromptTooltip,
  },
  data() {
    return {
      columns: [],
      tableData: [],
      loading: false,
    };
  },
  computed: {
    zoomValue() {
      return this.$store.state.newNavZoomValue;
    },
    currentSupply() {
      return (
        supplyOptions.find((v) => v.value === this.performance)?.label ?? ""
      );
    },
  },
  filters: {
    promptContent(value) {
      return {
        "JD Self-run": "Weighed By LQ Pan - Geo SO%",
        Total:
          "Weighted by LQ Business Type SO% and only include data for the Biz Types that need to be assessed (non-gray).",
      }[value];
    },
  },
  watch: {
    loading(val) {
      this.$emit("update-loading", val);
    },
    lob(val) {
      this.getSummaryDetail(val);
    },
  },
  mounted() {
    this.init();
  },
  methods: {
    init() {
      this.getSummaryDetail(this.lob);
    },
    getSummaryDetail(lob) {
      if (!this.hasLob) return;
      this.loading = true;
      diagnosisInventoryService
        .getSummaryDetail({ lob })
        .then(({ code, data }) => {
          if (code !== 0) {
            // TODO: error handle
            return;
          }
          const { columns, details } = data;
          this.columns = formatSummaryDetailColumns(columns);
          this.tableData = formatSummaryDetailData(
            details,
            this.lob === "iPhone"
          );
        })
        .catch((err) => {
          console.log(err);
        })
        .finally(() => {
          this.loading = false;
        });
    },
    calcNumHighlight(data, prop) {
      if (this.performance !== "in_stock") {
        return "";
      }

      const {
        origin_in_stock,
        yellow_light,
        red_light,
        in_whitelist = true,
      } = data;

      if (
        origin_in_stock === null ||
        origin_in_stock === undefined ||
        origin_in_stock === ""
      ) {
        return "";
      }

      if (!in_whitelist && prop !== "Total") {
        return "grey";
      }

      const red = getNumberWithTwoDecimals(red_light);
      const yellow = getNumberWithTwoDecimals(yellow_light);
      const current = getNumberWithTwoDecimals(origin_in_stock);
      if (isNumber(current) && isNumber(yellow) && current >= yellow) {
        return "";
      }
      if (
        isNumber(yellow) &&
        isNumber(red) &&
        current < yellow &&
        current >= red
      ) {
        return "warning";
      }
      if (isNumber(red) && current < red) {
        return "attention";
      }
      return "";
    },
    tableCellClassName({ rowIndex }) {
      if (this.tableData.length - 1 === rowIndex) {
        return "custom-table-last-cell";
      }
      return "";
    },

    supplyPoint(v) {
      sendLog({
        event_name: "smart_tower_today_Inventory_inwoiinv_click",
        event_type: "click",
        extra: {
          type: v,
        },
      });
    },
  },
};
</script>

<style lang="scss" scoped>
.header {
  @include flex(space-between);
  padding: 22px 0 16px;
}

.prompt-wrapper {
  @include flex(flex-start);
}

::v-deep .el-table {
  .el-table__header-wrapper {
    .el-table__header {
      .el-table__cell {
        padding-right: 0;
      }
    }
  }
  .el-table__body-wrapper {
    .el-table__body {
      .el-table__row {
        .el-table__cell {
          padding-right: 0;
        }
      }
    }
  }

  .el-table__fixed-right,
  .el-table__fixed {
    display: none;
  }

  .el-table__cell.is-hidden {
    .cell {
      visibility: visible;
    }
    &.col-total {
      position: sticky;
      right: 0;
      z-index: 3;
      overflow: visible;
      background: #fff;
    }
    &.col-left {
      position: sticky;
      left: 0;
      z-index: 3;
      overflow: visible;
      background: #fff;
    }
  }
  .el-table__header-wrapper .el-table__cell.is-hidden.col-total,
  .el-table__header-wrapper .el-table__cell.is-hidden.col-left {
    background: #f2f2f5;
  }

  .el-table__header-wrapper:has(+ .el-table__body-wrapper:not(.is-scrolling-right)),
  .el-table__body-wrapper:not(.is-scrolling-right) {
    .el-table__cell.is-hidden.col-total::before {
      content: "";
      position: absolute;
      top: 0;
      left: -10px;
      bottom: 0;
      width: 10px;
      background: linear-gradient(
        to left,
        rgba(0, 0, 0, 0.1) 0%,
        rgba(0, 0, 0, 0) 100%
      );
      pointer-events: none;
    }
  }
  .el-table__header-wrapper:has(+ .el-table__body-wrapper:not(.is-scrolling-left)),
  .el-table__body-wrapper:not(.is-scrolling-left) {
    .el-table__cell.is-hidden.col-left::before {
      content: "";
      position: absolute;
      top: 0;
      right: -10px;
      bottom: 0;
      width: 10px;
      background: linear-gradient(
        to right,
        rgba(0, 0, 0, 0.1) 0%,
        rgba(0, 0, 0, 0) 100%
      );
      pointer-events: none;
    }
  }
}
</style>
<style>
.smart_tower_tool_tip_Total {
  max-width: 380px;
  line-height: 16px;
}
</style>
