<template>
  <div>
    <div class="title">
      <div class="panel-title">
        <div>
          {{ lob }} Out-of-stock Status
          <div
            v-for="subTitle in subTitles"
            :key="subTitle"
            class="panel-sub-title"
          >
            {{ subTitle }}
          </div>
        </div>
        <CapsuleTab
          v-model="typeValue"
          :options="TypeOptions"
          size="small"
          :equal="false"
          :disabled="loading"
          v-if="isProduct"
        />
      </div>
      <div class="filter">
        <CustomSelect
          v-if="!isProduct"
          size="small"
          v-model="subLobValue"
          :options="subLobOptions"
        >
          <template #prefix>Sub-LOB: </template>
        </CustomSelect>
        <CustomCascader
          v-if="!isAdmin"
          :key="view"
          ref="cascader"
          v-model="productRtmValue"
          :options="rtmOptions"
          :props="{
            expandTrigger: true,
            checkStrictly: true,
            children: isProduct ? '' : 'subrtm',
          }"
          :show-all-levels="true"
          :prefix="isProduct ? 'RTM' : 'RTM / Sub-RTM'"
          class="cascader"
          @input="handleRtmChange"
        />
        <CustomSelect
          v-if="isAdmin && isReseller"
          size="small"
          v-model="subRtmValue"
          :options="subRtmOptions"
          @change="handleSubRtmChange"
        >
          <template #prefix>Sub-RTM: </template>
        </CustomSelect>
        <VirtualSelect
          v-if="isReseller || isPOS"
          size="small"
          v-model="resellerValue"
          :options="resellerOptions"
          filterable
          searchKey="value"
          @change="handleResellerChange"
        >
          <template #prefix>Reseller: </template>
        </VirtualSelect>
        <VirtualSelect
          v-if="isPOS"
          size="small"
          v-model="posValue"
          :options="posOptions"
          filterable
          searchKey="value"
        >
          <template #prefix>POS: </template>
        </VirtualSelect>
      </div>
    </div>
    <VirtualScroll
      ref="virtual"
      :key="view + lob"
      :data="tableData"
      :item-size="44 * zoomValue"
      :dynamic="false"
      :buffer="440"
      :key-prop="virtualKeyProp"
      @change="(virtualList) => (virtualData = virtualList)"
      v-tool-tip.delegate="{ targetClassName: 'break-word' }"
    >
      <el-table
        v-load="loading"
        :class="{ 'not-100': hasFixed }"
        :cell-style="tableCellStyle(columns, tableData, 'body', view, time)"
        :header-cell-style="
          tableCellStyle(columns, tableData, 'header', view, time)
        "
        :data="virtualData"
        :row-key="virtualKeyProp"
        v-bind="tableHeight"
      >
        <el-table-column
          v-for="(column, index) in columns"
          v-bind="column"
          :key="column.prop + index"
          :width="column?.width * zoomValue"
        >
          <template #header>
            <template v-if="column.prop === 'Reseller'">
              {{ column.label }}
              {{ resellerDataLength ? `(${resellerDataLength})` : "" }}
            </template>
            <template v-else-if="column.prop === 'POS'">
              {{ column.label }}
              {{ posDataLength ? `(${posDataLength})` : "" }}
            </template>
            <template v-else>{{ column.label }}</template>
          </template>
          <template slot-scope="scope">
            <span
              v-if="index === 0 && scope.row[column.prop].includes('Total')"
              class="tip-wrapper"
            >
              <svg-icon data_iconName="diagnosis-inventory-total" />
              <span>{{ scope.row[column.prop] }}</span>
            </span>
            <span
              v-else-if="
                [
                  'Sub-LOB',
                  'RTM',
                  'Biz Type',
                  'Inv. Points#',
                  'POS#',
                  'Reseller',
                  'POS',
                ].includes(column.prop)
              "
              class="break-word"
            >
              {{ scope.row[column.prop] }}
            </span>
            <template v-else>
              <span :class="scope.row?.[column.prop]?.color">
                <template v-if="!scope.row?.[column.prop]?.resolve">
                  {{ scope.row?.[column.prop]?.value ?? "-" }}
                </template>
                <template v-else>
                  {{ scope.row?.[column.prop]?.[typeValue] ?? "-" }}
                </template>
              </span>
            </template>
          </template>
        </el-table-column>
        <template slot="empty">
          <TableNoData :templateType="loading ? '' : 'noData'" />
        </template>
      </el-table>
    </VirtualScroll>
  </div>
</template>

<script>
import TableNoData from "@/pages/Diagnosis/components/TableNoData.vue";
import diagnosisInventoryService from "@/services/DiagnosisInventoryService";
import CustomSelect from "@/components/CustomSelect.vue";
import CustomCascader from "@/pages/DirectShipment/components/CustomCascader.vue";
import { VirtualScroll } from "@/components/VirtualScroll";
import VirtualSelect from "../../../components/VirtualSelect.vue";
import { formatOutOfStockColumns, formatWeeklyData } from "../format";
import OutOfStockMixin from "./OutOfStockMixin";
import CapsuleTab from "@/components/CapsuleTab.vue";

export default {
  props: {
    lob: {
      type: String,
      default: "",
    },
    time: {
      type: String,
      default: "",
    },
    date: {
      type: String,
      default: "",
    },
    view: {
      type: String,
      default: "",
    },
    hasLob: {
      type: Boolean,
      default: false,
    },
    performance: {
      type: String,
      default: "",
    },
    subLobOptions: {
      type: Array,
      default: () => [],
    },
    RtmMap: {
      type: Array,
      default: () => [],
    },
    isAdmin: {
      type: Boolean,
      default: true,
    },
  },
  components: {
    TableNoData,
    CustomSelect,
    CustomCascader,
    VirtualScroll,
    VirtualSelect,
    CapsuleTab,
  },
  mixins: [OutOfStockMixin],
  data() {
    return {
      columns: [],
      tableData: [],
      virtualData: [],

      subLobValue: "Total",
    };
  },
  computed: {
    resellerDataLength() {
      return this.tableData.filter((item) => !item.Reseller.includes("Total"))
        .length;
    },
    posDataLength() {
      return this.tableData.filter((item) => !item.POS.includes("Total"))
        .length;
    },
    condition() {
      const obj = {
        lob: this.lob,
        date: this.date,
        view_type: this.view,
      };
      if (this.isAdmin) {
        if (this.isRTM) {
          return {
            ...obj,
            sub_lob: this.subLobValue,
          };
        }
        if (this.isReseller) {
          return {
            ...obj,
            reseller_id: this.resellerValue,
            sub_rtm: this.subRtmValue,
            sub_lob: this.subLobValue,
          };
        }
        return obj;
      } else {
        if (this.isProduct) {
          return {
            ...obj,
            rtm: this.rtmValue,
          };
        } else if (this.isPOS) {
          return {
            ...obj,
            sub_lob: this.subLobValue,
            rtm: this.rtmValue,
            sub_rtm: this.subRtmValue,
            reseller_id: this.resellerValue,
            pos_id: this.posValue,
          };
        } else {
          return {
            ...obj,
            sub_lob: this.subLobValue,
            rtm: this.rtmValue,
            sub_rtm: this.subRtmValue,
            reseller_id: this.resellerValue,
          };
        }
      }
    },
    hasFixed() {
      return this.columns.some((item) => item?.fixed);
    },
  },
  methods: {
    getData(params) {
      if (!this.hasLob) return;
      this.loading = true;

      if ("reseller_id" in params) {
        params.reseller_id = params.reseller_id.includes("@")
          ? params.reseller_id.split("@")[0]
          : params.reseller_id;
      }

      if ("pos_id" in params) {
        params.pos_id = params.pos_id.includes("@")
          ? params.pos_id.split("@")[0]
          : params.pos_id;
      }

      diagnosisInventoryService
        .getWeeklyOutOfStock(params)
        .then(({ code = 0, data = {} }) => {
          if (code !== 0) return;
          const { columns = [], result = [], _columns = [] } = data;
          this.columns = formatOutOfStockColumns(
            _columns?.length ? _columns : columns,
            this.view,
            this.time,
            this.isAdmin,
            this.lob
          );
          this.tableData = formatWeeklyData(
            result,
            this.isAdmin,
            this.view,
            this.lob
          );
          this.$nextTick(() => {
            this.$refs?.virtual?.renderAllData?.();
          });
        })
        .catch((err) => {
          console.log(err);
        })
        .finally(() => {
          this.loading = false;
        });
    },
  },
};
</script>

<style lang="scss" scoped>
.title {
  padding: 24px 0 16px;

  .panel-title {
    @include flex();
    font-size: 16px;
    line-height: 20px;

    .panel-sub-title {
      @include font(13px, 16px, #aeaeb2);
      margin-top: 4px;
      font-weight: 400;
    }
  }
  .panel-tip {
    @include font(13px, 16px, #aeaeb2);
    margin-top: 4px;
    font-weight: 400;
  }
}

.filter {
  @include flex(flex-start);
  gap: 16px;
  margin-top: 16px;

  ::v-deep .select-wrap {
    width: 312px;
  }

  .cascader {
    width: 312px;
    height: 40px;
    ::v-deep .el-input--suffix {
      white-space: nowrap;
    }
    ::v-deep .el-cascader__dropdown .el-cascader-menu__wrap {
      height: 100%;
      max-height: 210px;
    }
  }
}
</style>
