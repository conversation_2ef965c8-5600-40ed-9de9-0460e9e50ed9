import { isString } from "./utils";

const DATE_TIME_FORMAT = "YYYY-MM-DD HH:mm:ss";

export function joinTimestamp(join, restful = false) {
  if (!join) {
    return restful ? "" : {};
  }
  const now = new Date().getTime();
  if (restful) {
    return `?_t=${now}`;
  }
  return { _t: now };
}

/**
 * @description: Format request parameter time
 */
export function formatRequestDate(params) {
  return formatRequestParams(params, {
    formatDate: true,
    trimStrings: true,
    deep: true,
  });
}

/**
 * @param {any} params - 要格式化的参数
 * @param {Object} options - 配置选项
 * @param {boolean} options.formatDate - 是否格式化日期，默认 true
 * @param {boolean} options.trimStrings - 是否去除字符串空格，默认 true
 * @param {boolean} options.deep - 是否深度处理，默认 true
 * @param {Set} visited - 内部使用，防止循环引用
 */
function formatRequestParams(params, options = {}, visited = new Set()) {
  const { formatDate = true, trimStrings = true, deep = true } = options;

  // 防止循环引用
  if (visited.has(params)) {
    console.warn("检测到循环引用，跳过处理");
    return params;
  }

  // 只处理普通对象
  if (!isPlainObject(params)) {
    return params;
  }

  visited.add(params);

  try {
    for (const key in params) {
      if (!Object.prototype.hasOwnProperty.call(params, key)) {
        continue;
      }

      const value = params[key];

      // 处理日期格式化
      if (formatDate && hasFormatMethod(value)) {
        params[key] = formatDateValue(value);
        continue;
      }

      // 处理字符串去空格
      if (trimStrings && isString(value)) {
        params[key] = trimStringValue(value);
        continue;
      }

      // 递归处理嵌套对象
      if (deep && isPlainObject(value)) {
        formatRequestParams(value, options, visited);
      }
    }
  } finally {
    visited.delete(params);
  }

  return params;
}

/**
 * 检查是否为普通对象（排除数组、Date、RegExp等）
 */
function isPlainObject(obj) {
  return (
    obj !== null &&
    typeof obj === "object" &&
    Object.prototype.toString.call(obj) === "[object Object]"
  );
}

/**
 * 检查对象是否有 format 方法（通常是日期对象）
 */
function hasFormatMethod(value) {
  return value && typeof value.format === "function";
}

/**
 * 格式化日期值
 */
function formatDateValue(dateValue) {
  try {
    return dateValue.format(DATE_TIME_FORMAT);
  } catch (error) {
    console.warn("日期格式化失败:", error.message);
    return dateValue; // 返回原值
  }
}

/**
 * 安全地去除字符串空格
 */
function trimStringValue(stringValue) {
  try {
    return stringValue.trim();
  } catch (error) {
    console.warn("字符串去空格失败:", error.message);
    return stringValue; // 返回原值
  }
}

export const formatRestful = (config, params, joinTime = true) => {
  config.url = config.url + params + `${joinTimestamp(joinTime, true)}`;
  config.params = undefined;
};
