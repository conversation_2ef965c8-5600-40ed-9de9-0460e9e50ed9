<template>
  <div>
    <div class="head-filter" v-if="isAdmin ? !isProduct : true">
      <CustomSelect
        v-if="!isProduct"
        size="small"
        v-model="subLobValue"
        :options="subLobOptions"
      >
        <template #prefix>Sub-LOB: </template>
      </CustomSelect>
      <CustomCascader
        v-if="!isAdmin"
        :key="view"
        ref="cascader"
        v-model="productRtmValue"
        :options="rtmOptions"
        :props="{
          expandTrigger: true,
          checkStrictly: true,
          children: 'subrtm',
        }"
        :show-all-levels="true"
        prefix="RTM / Sub-RTM"
        class="cascader"
        @input="handleRtmChange"
      />
      <CustomSelect
        v-if="isAdmin && isReseller"
        size="small"
        v-model="subRtmValue"
        :options="subRtmOptions"
        @change="handleSubRtmChange"
      >
        <template #prefix>Sub-RTM: </template>
      </CustomSelect>
      <VirtualSelect
        v-if="isReseller || isPOS"
        size="small"
        v-model="resellerValue"
        :options="resellerOptions"
        filterable
        searchKey="value"
        @change="handleResellerChange"
      >
        <template #prefix>Reseller: </template>
      </VirtualSelect>
      <VirtualSelect
        v-if="isPOS"
        size="small"
        v-model="posValue"
        :options="posOptions"
        filterable
        searchKey="value"
      >
        <template #prefix>POS: </template>
      </VirtualSelect>
    </div>
    <div class="title">
      <div class="panel-title">
        <div>
          {{ lob }} Out-of-stock status
          <div
            v-for="subTitle in subTitles"
            :key="subTitle"
            class="panel-sub-title"
          >
            {{ subTitle }}
          </div>
        </div>
        <CapsuleTab
          v-model="typeValue"
          :options="TypeOptions"
          size="small"
          :equal="false"
          :disabled="loading"
          v-if="isProduct"
        />
      </div>
    </div>
    <VirtualScroll
      ref="virtualStatus"
      :key="view + lob + 'Status'"
      :data="tableDataStatus"
      :item-size="44 * zoomValue"
      :dynamic="false"
      :buffer="440"
      :key-prop="virtualKeyProp"
      @change="(virtualList) => (virtualDataStatus = virtualList)"
      v-tool-tip.delegate="{ targetClassName: 'break-word' }"
    >
      <el-table
        :key="view + 'ElStatus'"
        v-load="loading"
        :class="{ 'not-100': statusHasFixed }"
        :cell-style="
          tableCellStyle(columnsStatus, tableDataStatus, 'body', view, time)
        "
        :header-cell-style="
          tableCellStyle(columnsStatus, tableDataStatus, 'header', view, time)
        "
        :data="virtualDataStatus"
        :row-key="virtualKeyProp"
        v-bind="tableHeight"
      >
        <el-table-column
          v-for="(column, index) in columnsStatus"
          v-bind="column"
          :key="column.prop + index"
          :width="column?.width * zoomValue"
        >
          <template #header>
            <template v-if="column.prop === 'Reseller'">
              {{ column.label }}
              {{
                statusResellerDataLength ? `(${statusResellerDataLength})` : ""
              }}
            </template>
            <template v-else-if="column.prop === 'POS'">
              {{ column.label }}
              {{ statusPOSDataLength ? `(${statusPOSDataLength})` : "" }}
            </template>
            <template v-else>{{ column.label }}</template>
          </template>
          <template slot-scope="scope">
            <span
              v-if="index === 0 && scope.row[column.prop].includes('Total')"
              class="tip-wrapper"
            >
              <svg-icon data_iconName="diagnosis-inventory-total" />
              <span>{{ scope.row[column.prop] }}</span>
            </span>
            <span
              v-else-if="
                [
                  'Sub-LOB',
                  'RTM',
                  'Biz Type',
                  'Inv. Points#',
                  'POS#',
                  'Reseller',
                  'POS',
                ].includes(column.prop)
              "
              class="break-word"
            >
              {{ scope.row[column.prop] }}
            </span>
            <template v-else>
              <span :class="scope.row?.[column.prop]?.color">
                <template v-if="!scope.row?.[column.prop]?.resolve">
                  {{ scope.row?.[column.prop]?.value ?? "-" }}
                </template>
                <template v-else>
                  {{ scope.row?.[column.prop]?.[typeValue] ?? "-" }}
                </template>
              </span>
            </template>
          </template>
        </el-table-column>
        <template slot="empty">
          <TableNoData :templateType="loading ? '' : 'noData'" />
        </template>
      </el-table>
    </VirtualScroll>
    <div class="title">
      <div class="panel-title">{{ lob }} Out-of-stock Scoring</div>
      <div class="filter" v-if="isAdmin && isProduct">
        <CustomSelect
          size="small"
          v-model="subRtmValue"
          :options="subRtmOptions"
          @change="handleSubRtmChange"
        >
          <template #prefix>Sub-RTM: </template>
        </CustomSelect>
      </div>
    </div>
    <VirtualScroll
      ref="virtualScoring"
      :key="view + lob + 'Scoring'"
      :data="tableDataScoring"
      :item-size="44 * zoomValue"
      :dynamic="false"
      :buffer="440"
      :key-prop="virtualKeyProp"
      @change="(virtualList) => (virtualDataScoring = virtualList)"
      v-tool-tip.delegate="{ targetClassName: 'break-word' }"
    >
      <el-table
        :key="view + 'ElScoring'"
        v-load="loading"
        :class="{ 'not-100': scoringHasFixed }"
        :cell-style="scoringCellStyle(columnsScoring, 'body')"
        :header-cell-style="scoringCellStyle(columnsScoring, 'header')"
        :data="virtualDataScoring"
        :row-key="virtualKeyProp"
        v-bind="tableHeight"
      >
        <el-table-column
          v-for="(column, index) in columnsScoring"
          v-bind="column"
          :key="column.prop + index"
          :width="column?.width * zoomValue"
        >
          <template #header>
            <template v-if="column.prop === 'Reseller'">
              {{ column.label }}
              {{
                scoringResellerDataLength
                  ? `(${scoringResellerDataLength})`
                  : ""
              }}
            </template>
            <template v-else-if="column.prop === 'POS'">
              {{ column.label }}
              {{ scoringPOSDataLength ? `(${scoringPOSDataLength})` : "" }}
            </template>
            <template v-else>{{ column.label }}</template>
          </template>
          <template slot-scope="scope">
            <span
              v-if="index === 0 && scope.row[column.prop].includes('Total')"
              class="tip-wrapper"
            >
              <svg-icon data_iconName="diagnosis-inventory-total" />
              <span>{{ scope.row[column.prop] }}</span>
            </span>
            <span
              v-else-if="
                [
                  'Sub-LOB',
                  'RTM',
                  'Biz Type',
                  'Inv. Points#',
                  'POS#',
                  'Reseller',
                  'POS',
                ].includes(column.prop)
              "
              class="break-word"
            >
              {{ scope.row[column.prop] }}
            </span>
            <span v-else :class="scope.row?.[column.prop]?.color">
              {{ scope.row?.[column.prop]?.value ?? "-" }}
            </span>
          </template>
        </el-table-column>
        <template slot="empty">
          <TableNoData :templateType="loading ? '' : 'noData'" />
        </template>
      </el-table>
    </VirtualScroll>
  </div>
</template>

<script>
import TableNoData from "@/pages/Diagnosis/components/TableNoData.vue";
import diagnosisInventoryService from "@/services/DiagnosisInventoryService";
import CustomSelect from "@/components/CustomSelect.vue";
import CustomCascader from "@/pages/DirectShipment/components/CustomCascader.vue";
import { VirtualScroll } from "@/components/VirtualScroll";
import VirtualSelect from "../../../components/VirtualSelect.vue";
import { formatOutOfStockColumns, formatQuarterlyData } from "../format";
import OutOfStockMixin from "./OutOfStockMixin";
import CapsuleTab from "@/components/CapsuleTab.vue";

export default {
  props: {
    lob: {
      type: String,
      default: "",
    },
    time: {
      type: String,
      default: "",
    },
    date: {
      type: String,
      default: "",
    },
    view: {
      type: String,
      default: "",
    },
    hasLob: {
      type: Boolean,
      default: false,
    },
    performance: {
      type: String,
      default: "",
    },
    subLobOptions: {
      type: Array,
      default: () => [],
    },
    RtmMap: {
      type: Array,
      default: () => [],
    },
    isAdmin: {
      type: Boolean,
      default: true,
    },
  },
  components: {
    TableNoData,
    CustomSelect,
    CustomCascader,
    VirtualScroll,
    VirtualSelect,
    CapsuleTab,
  },
  mixins: [OutOfStockMixin],
  data() {
    return {
      columnsStatus: [],
      tableDataStatus: [],
      virtualDataStatus: [],
      columnsScoring: [],
      tableDataScoring: [],
      virtualDataScoring: [],

      subLobValue: "Total",
    };
  },
  computed: {
    statusResellerDataLength() {
      return this.tableDataStatus.filter(
        (item) => !item.Reseller.includes("Total")
      ).length;
    },
    scoringResellerDataLength() {
      return this.tableDataScoring.filter(
        (item) => !item.Reseller.includes("Total")
      ).length;
    },
    statusPOSDataLength() {
      return this.tableDataStatus.filter((item) => !item.POS.includes("Total"))
        .length;
    },
    scoringPOSDataLength() {
      return this.tableDataScoring.filter((item) => !item.POS.includes("Total"))
        .length;
    },
    condition() {
      const obj = {
        lob: this.lob,
        date: this.date,
        view_type: this.view,
      };
      if (this.isAdmin) {
        if (this.isProduct) {
          return {
            ...obj,
            sub_rtm: this.subRtmValue,
          };
        }
        if (this.isRTM) {
          return {
            ...obj,
            sub_lob: this.subLobValue,
          };
        }
        return {
          ...obj,
          sub_lob: this.subLobValue,
          sub_rtm: this.subRtmValue,
          reseller_id: this.resellerValue,
        };
      } else {
        if (this.isProduct) {
          return {
            ...obj,
            rtm: this.rtmValue,
            sub_rtm: this.subRtmValue,
          };
        } else if (this.isPOS) {
          return {
            ...obj,
            sub_lob: this.subLobValue,
            rtm: this.rtmValue,
            sub_rtm: this.subRtmValue,
            reseller_id: this.resellerValue,
            pos_id: this.posValue,
          };
        } else {
          return {
            ...obj,
            sub_lob: this.subLobValue,
            rtm: this.rtmValue,
            sub_rtm: this.subRtmValue,
            reseller_id: this.resellerValue,
          };
        }
      }
    },
    statusHasFixed() {
      return this.columnsStatus.some((item) => item?.fixed);
    },
    scoringHasFixed() {
      return this.columnsScoring.some((item) => item?.fixed);
    },
  },
  methods: {
    getData(params) {
      if (!this.hasLob) return;
      this.loading = true;

      if ("reseller_id" in params) {
        params.reseller_id = params.reseller_id.includes("@")
          ? params.reseller_id.split("@")[0]
          : params.reseller_id;
      }

      if ("pos_id" in params) {
        params.pos_id = params.pos_id.includes("@")
          ? params.pos_id.split("@")[0]
          : params.pos_id;
      }

      diagnosisInventoryService
        .getQuarterlyOutOfStock(params)
        .then(({ code = 0, data = {} }) => {
          if (code !== 0) return;
          const {
            status: {
              columns: statusColumns = [],
              result: statusResult = [],
              _columns: statusColumns2 = [],
            },
            scoring: {
              columns: scoringColumns = [],
              result: scoringResult = [],
            },
          } = data;
          this.columnsStatus = formatOutOfStockColumns(
            statusColumns2?.length ? statusColumns2 : statusColumns,
            this.view,
            this.time,
            this.isAdmin,
            this.lob
          );
          this.tableDataStatus = formatQuarterlyData(
            statusResult,
            this.isAdmin,
            this.view,
            this.lob
          );
          this.columnsScoring = formatOutOfStockColumns(
            scoringColumns,
            this.view,
            this.time,
            this.isAdmin,
            this.lob
          );
          this.tableDataScoring = formatQuarterlyData(
            scoringResult,
            this.isAdmin,
            this.view,
            this.lob
          );
          this.$nextTick(() => {
            this.$refs?.virtualStatus?.renderAllData?.();
            this.$refs?.virtualScoring?.renderAllData?.();
          });
        })
        .catch((err) => {
          console.log(err);
        })
        .finally(() => {
          this.loading = false;
        });
    },

    scoringCellStyle(columns = [], cellType = "body") {
      return ({ column }) => {
        const currentColumn = columns.find(
          (item) => item.prop === column.property
        );
        let style = "";
        if (currentColumn?.fixed) {
          style += `left: ${Math.ceil(
            currentColumn?.left * this.zoomValue
          )}px;`;
          if (cellType === "body") {
            style += "background-color: #fff;";
          }
        }
        return style;
      };
    },
  },
};
</script>

<style lang="scss" scoped>
.title {
  padding: 24px 0 16px;

  .panel-title {
    @include flex();
    font-size: 16px;
    line-height: 20px;

    .panel-sub-title {
      @include font(13px, 16px, #aeaeb2);
      margin-top: 4px;
      font-weight: 400;
    }
  }
  .panel-tip {
    @include font(13px, 16px, #aeaeb2);
    margin-top: 4px;
    font-weight: 400;
  }
}

.filter,
.head-filter {
  @include flex(flex-start);
  margin-left: auto;
  gap: 16px;
  margin-top: 16px;

  ::v-deep .select-wrap {
    width: 312px;
  }

  .cascader {
    width: 312px;
    height: 40px;
    ::v-deep .el-input--suffix {
      white-space: nowrap;
    }
    ::v-deep .el-cascader__dropdown .el-cascader-menu__wrap {
      height: 100%;
      max-height: 210px;
    }
  }
}
.head-filter {
  margin-top: 20px;
}
</style>
