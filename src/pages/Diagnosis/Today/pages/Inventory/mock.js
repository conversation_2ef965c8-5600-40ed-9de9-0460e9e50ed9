export const CheckData = {
  code: 0,
  data: {
    col_name: ["Township", "Mass Merchant", "OTC", "Duty Free"],
    row_data: [
      {
        "Duty Free": 12,
        "Mass Merchant": 8,
        OTC: 5,
        Township: 3,
        sub_lob: "Total",
      },
      {
        "Duty Free": "MG6W4CH/A",
        "Mass Merchant": "-",
        OTC: "-",
        Township: "-",
        points: "MG6W4CH/A",
        sub_lob: "iPhone 17",
      },
      {
        "Duty Free": "MG6X4CH/A",
        "Mass Merchant": "-",
        OTC: "-",
        Township: "-",
        points: "MG6X4CH/A",
        sub_lob: "iPhone 17",
      },
      {
        "Duty Free": "MG734CH/A",
        "Mass Merchant": "-",
        OTC: "-",
        Township: "-",
        points: "MG734CH/A",
        sub_lob: "iPhone 17",
      },
      {
        "Duty Free": "-",
        "Mass Merchant": "256GB",
        OTC: "256GB",
        Township: "-",
        points: "256GB",
        sub_lob: "iPhone 17",
      },
      {
        "Duty Free": "-",
        "Mass Merchant": "-",
        OTC: "-",
        Township: "All",
        points: "iPhone 17",
        sub_lob: "iPhone 17",
      },
      {
        "Duty Free": "MG8U4CH/A",
        "Mass Merchant": "MG8U4CH/A",
        OTC: "-",
        Township: "-",
        points: "MG8U4CH/A",
        sub_lob: "iPhone 17 Pro",
      },
      {
        "Duty Free": "MG8T4CH/A",
        "Mass Merchant": "MG8T4CH/A",
        OTC: "-",
        Township: "-",
        points: "MG8T4CH/A",
        sub_lob: "iPhone 17 Pro",
      },
      {
        "Duty Free": "MG8W4CH/A",
        "Mass Merchant": "MG8W4CH/A",
        OTC: "-",
        Township: "-",
        points: "MG8W4CH/A",
        sub_lob: "iPhone 17 Pro",
      },
      {
        "Duty Free": "MG904CH/A",
        "Mass Merchant": "-",
        OTC: "-",
        Township: "-",
        points: "MG904CH/A",
        sub_lob: "iPhone 17 Pro",
      },
      {
        "Duty Free": "-",
        "Mass Merchant": "-",
        OTC: "256GB",
        Township: "256GB",
        points: "256GB",
        sub_lob: "iPhone 17 Pro",
      },
      // {
      //   "Duty Free": "MG044CH/A",
      //   "Mass Merchant": "MG044CH/A",
      //   OTC: "-",
      //   Township: "-",
      //   points: "MG044CH/A",
      //   sub_lob: "iPhone 17 Pro Max",
      // },
      // {
      //   "Duty Free": "MG034CH/A",
      //   "Mass Merchant": "MG034CH/A",
      //   OTC: "-",
      //   Township: "-",
      //   points: "MG034CH/A",
      //   sub_lob: "iPhone 17 Pro Max",
      // },
      // {
      //   "Duty Free": "MG074CH/A",
      //   "Mass Merchant": "-",
      //   OTC: "-",
      //   Township: "-",
      //   points: "MG074CH/A",
      //   sub_lob: "iPhone 17 Pro Max",
      // },
      // {
      //   "Duty Free": "MG064CH/A",
      //   "Mass Merchant": "MG064CH/A",
      //   OTC: "-",
      //   Township: "-",
      //   points: "MG064CH/A",
      //   sub_lob: "iPhone 17 Pro Max",
      // },
      // {
      //   "Duty Free": "MG094CH/A",
      //   "Mass Merchant": "-",
      //   OTC: "-",
      //   Township: "-",
      //   points: "MG094CH/A",
      //   sub_lob: "iPhone 17 Pro Max",
      // },
      // {
      //   "Duty Free": "-",
      //   "Mass Merchant": "-",
      //   OTC: "256GB",
      //   Township: "-",
      //   points: "256GB",
      //   sub_lob: "iPhone 17 Pro Max",
      // },
      // {
      //   "Duty Free": "-",
      //   "Mass Merchant": "-",
      //   OTC: "-",
      //   Township: "All",
      //   points: "iPhone 17 Pro Max",
      //   sub_lob: "iPhone 17 Pro Max",
      // },
      // {
      //   "Duty Free": "-",
      //   "Mass Merchant": "128GB",
      //   OTC: "128GB",
      //   Township: "-",
      //   points: "128GB",
      //   sub_lob: "iPhone 16",
      // },
      // {
      //   "Duty Free": "-",
      //   "Mass Merchant": "-",
      //   OTC: "All",
      //   Township: "-",
      //   points: "iPhone 15",
      //   sub_lob: "iPhone 15",
      // },
    ],
  },
  msg: "ok",
};
