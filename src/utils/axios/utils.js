import { intersectionWith, isEqual, mergeWith, unionWith } from "lodash-es";

const toString = Object.prototype.toString;

export function is(val, type) {
  return toString.call(val) === `[object ${type}]`;
}

export function isObject(val) {
  return val !== null && is(val, "Object");
}

export function isEmpty(val) {
  if (isArray(val) || isString(val)) {
    return val.length === 0;
  }

  if (val instanceof Map || val instanceof Set) {
    return val.size === 0;
  }

  if (isObject(val)) {
    return Object.keys(val).length === 0;
  }

  return false;
}

export function isDate(val) {
  return is(val, "Date");
}

export function isNull(val) {
  return val === null;
}

export function isNumber(val) {
  return is(val, "Number");
}

// export function isPromise<T = any>(val: unknown): val is Promise<T> {
//   return (
//     is(val, "Promise") &&
//     isObject(val) &&
//     isFunction(val.then) &&
//     isFunction(val.catch)
//   );
// }

export function isString(val) {
  return is(val, "String");
}

export function isFunction(val) {
  return typeof val === "function";
}

export function isBoolean(val) {
  return is(val, "Boolean");
}

export function isRegExp(val) {
  return is(val, "RegExp");
}

export function isArray(val) {
  return val && Array.isArray(val);
}

export function isWindow(val) {
  return typeof window !== "undefined" && is(val, "Window");
}

export function isElement(val) {
  return isObject(val) && !!val.tagName;
}

export function isMap(val) {
  return is(val, "Map");
}

export const isServer = typeof window === "undefined";

export const isClient = !isServer;

export function isUrl(path) {
  const reg = /^http(s)?:\/\/([\w-]+\.)+[\w-]+(\/[\w- ./?%&=]*)?/;
  return reg.test(path);
}

/**
 * Add the object as a parameter to the URL
 * @param baseUrl url
 * @param obj
 * @returns {string}
 * eg:
 *  let obj = {a: '3', b: '4'}
 *  setObjToUrlParams('www.baidu.com', obj)
 *  ==>www.baidu.com?a=3&b=4
 */
export function setObjToUrlParams(baseUrl, obj) {
  let parameters = "";
  for (const key in obj) {
    parameters += key + "=" + encodeURIComponent(obj[key]) + "&";
  }
  parameters = parameters.replace(/&$/, "");
  return /\?$/.test(baseUrl)
    ? baseUrl + parameters
    : baseUrl.replace(/\/?$/, "?") + parameters;
}

/**
 * Recursively merge two objects.
 * 递归合并两个对象。
 *
 */
export function deepMerge(source, target, mergeArrays) {
  if (!target) {
    return source;
  }
  if (!source) {
    return target;
  }
  return mergeWith({}, source, target, (sourceValue, targetValue) => {
    if (isArray(targetValue) && isArray(sourceValue)) {
      switch (mergeArrays) {
        case "union":
          return unionWith(sourceValue, targetValue, isEqual);
        case "intersection":
          return intersectionWith(sourceValue, targetValue, isEqual);
        case "concat":
          return sourceValue.concat(targetValue);
        case "replace":
          return targetValue;
        default:
          throw new Error(`Unknown merge array strategy: ${mergeArrays}`);
      }
    }
    if (isObject(targetValue) && isObject(sourceValue)) {
      return deepMerge(sourceValue, targetValue, mergeArrays);
    }
    return undefined;
  });
}
