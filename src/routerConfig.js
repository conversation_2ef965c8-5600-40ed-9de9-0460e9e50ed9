import Vue from "vue";
import Router from "vue-router";
import { userService } from "./services/UserService";
import {
  CUSTOMIZED_EXP_ROUTES,
  STD_EXP_ROUTES,
} from "./pages/tool/abtest/routes";
import { INITIATIVES_TRACKING } from "./pages/Experiment/routes/routes-abtest-initiatives";
import { STAMP_ROUTES } from "@/pages/Event/routes";
import {
  STD_MULTI_EXP_ROUTES,
  STD_CARRIER_EXP_ROUTES,
  CST_ONLINE_ROUTES,
  STD_MONO_EXP_ROUTES,
  STD_COMMON_ONLINE_EXP_ROUTES,
  EXP_SUMMARY_ROUTES,
} from "./pages/Experiment/routes";
import { MDF_ROUTER_MODULE } from "./pages/Mdf/routes";
import RouterView from "./pages/routerView.vue";
import {
  ROUTER_ALLOCATION,
  CPF,
  CHANNEL_ALLOCATION,
  FAST,
  HK_TW_EDU,
  HK_TW_ENT,
  ROLE_PLANNING_TEAM,
} from "@/constant/fast";
import { DASHBOARD_FAUCET } from "@/pages/Dashboard/Faucet/router";
import { RESOURCE_PLANNING_ROUTER_MODULE } from "@/pages/Dashboard/ResourcePlanning/routes";
import { DIAGNOSIS_ROUTER_MODULE } from "@/pages/Diagnosis/routes";
import { FAST_STANDER_ROUTER } from "@/pages/Fast/ForecastDemand/E2E/router";
import { FORECAST_PLATFORM } from "@/pages/ForecastPlatform/routes";
import { DashboardRouter } from "@/pages/Fast/Dashboard/router";
import { DataSourceRoute } from "@/pages/Fast/DataSource/route";
import { HITL_ROUTER_MODULE } from "@/pages/Analysis/Hitl/routes";
import { ChannelCompliance } from "@/pages/ChannelCompliance/routes";
import { AIProgram } from "@/pages/AIProgram/routes";
import { LLMPlayground } from "@/pages/LLMPlayground/routes";
import { RewardSystem } from "@/pages/RewardSystem/routes";
import { NPIAllocationRouter } from "@/pages/Fast/NPIAllocation/router";

Vue.use(Router);

// router config
export const rawRouter = [
  {
    path: "/home",
    name: "home",
    meta: {
      name: "home",
    },
    component: () => import("./pages/homeView.vue"),
    children: [
      {
        path: "/index-dashboard",
        name: "Dashboard",
        meta: {
          name: "Dashboard",
          display: true,
        },
        component: RouterView,
        children: [
          // {
          //   path: "/tower/real-time",
          //   name: "towerRealTime",
          //   meta: {
          //     name: "Real-time",
          //     onlyShowParent: true,
          //     display: true,
          //     permissionName: "Tower",
          //   },
          //   condition: () => userService.hasPlatform("Tower Real-Time"),
          //   component: () =>
          //     import("./pages/Tower/RealTime/RealTime/indexView.vue"),
          // },
          {
            path: "/daily-metrics",
            name: "Daily Metrics",
            meta: {
              tag: "Tower",
              alias: "Tower",
              name: "Daily Metrics",
              display: true,
              permissionName: "Tower",
            },
            component: () => import("./pages/Tower/RealTime/indexView.vue"),
            children: [
              {
                path: "/tower1",
                name: "tower1",
                meta: {
                  name: "Tower",
                  display: true,
                  permissionName: "Tower",
                },
                condition: () => userService.hasPlatform("Tower_V1"),
                component: () => import("./pages/Tower/Daily/indexView.vue"),
                children: [
                  {
                    path: "/tower1/traffic-and-by-your-side",
                    name: "TrafficAndByYourSide",
                    meta: {
                      name: "Traffic & 在你身边",
                      parentPath: "/tower1",
                      permissionName: "Tower",
                    },
                    condition: () =>
                      userService.hasPlatform("Tower_V1") &&
                      userService.hasRoleName("Tower_V1", "Traffic"),
                    component: () =>
                      import("./pages/Tower/Daily/TrafficAndByYourSide.vue"),
                  },
                  {
                    path: "/tower1/so-performance",
                    name: "so-performance",
                    meta: {
                      name: "SO Performance",
                      parentPath: "/tower1",
                      permissionName: "Tower",
                    },
                    condition: () =>
                      userService.hasPlatform("Tower_V1") &&
                      userService.hasRoleName("Tower_V1", "SO"),
                    component: () =>
                      import("./pages/Tower/Daily/SoPerformance.vue"),
                  },
                  {
                    path: "/tower1/coverage-and-in-stock",
                    name: "CoverageAndInStock",
                    meta: {
                      name: "Coverage & In-Stock",
                      parentPath: "/tower1",
                      permissionName: "Tower",
                    },
                    condition: () =>
                      userService.hasPlatform("Tower_V1") &&
                      userService.hasRoleName("Tower_V1", "Coverage"),
                    component: () =>
                      import("./pages/Tower/Daily/CoverageAndInStock.vue"),
                  },
                  {
                    path: "/tower1/inventory-flow",
                    name: "InventoryFlow",
                    meta: {
                      name: "Inventory Flow",
                      parentPath: "/tower1",
                      permissionName: "Tower",
                    },
                    condition: () =>
                      userService.hasPlatform("Tower_V1") &&
                      userService.hasRoleName("Tower_V1", "Inventory"),
                    component: () =>
                      import("./pages/Tower/Daily/InventoryFlow.vue"),
                  },
                ],
              },
              {
                path: "/new-tower/beta",
                name: "towerBeta",
                meta: {
                  name: "Tower (beta)",
                  display: true,
                  permissionName: "Tower",
                },
                condition: () => userService.userRoles("Tower"),
                component: () => import("./pages/Tower/TowerBeta/Tower.vue"),
              },
            ],
          },
          {
            path: "/weekly",
            name: "Weekly",
            meta: {
              name: "Weekly Metrics",
              onlyShowParent: true,
              display: true,
              permissionName: "Weekly Sellout",
            },
            condition: () => {
              // return userService.hasPlatform("Operation");
              const hasOpPlatform = userService.hasPlatform("Operation");
              // const hasRole = ["Multi", "Online", "Carrier"].some((role) =>
              //   userService.hasRoleName("Operation", role)
              // );
              // return hasOpPlatform && hasRole;
              return hasOpPlatform;
            },
            redirect: () => {
              // if (userService.hasRoleName("Operation", "Mono")) {
              //   return { path: '/weekly/mono' }
              // }
              if (userService.hasRoleName("Operation", "Multi")) {
                return { path: "/weekly/multi" };
              }
              if (userService.hasRoleName("Operation", "Online")) {
                return { path: "/weekly/online" };
              }
              if (userService.hasRoleName("Operation", "Carrier")) {
                return { path: "/weekly/carrier" };
              }
            },
            component: () => import("./pages/operation/weekly/index.vue"),
            children: [
              {
                path: "/weekly/mono",
                name: "Mono",
                meta: {
                  name: "Mono",
                  display: false,
                  parentPath: "/weekly",
                  permissionName: "Weekly Sellout",
                },
                // condition: () => userService.hasRoleName("Operation", "Mono"),
                condition: () => false,
                component: () => import("./pages/operation/weekly/Weekly.vue"),
              },
              {
                path: "/weekly/multi",
                name: "Multi",
                meta: {
                  name: "Multi",
                  display: false,
                  parentPath: "/weekly",
                  permissionName: "Weekly Sellout",
                },
                condition: () => userService.hasRoleName("Operation", "Multi"),
                component: () => import("./pages/operation/sellout/index.vue"),
              },
              {
                path: "/weekly/online",
                name: "Online",
                meta: {
                  name: "Online",
                  display: false,
                  parentPath: "/weekly",
                  permissionName: "Weekly Sellout",
                },
                condition: () => userService.hasRoleName("Operation", "Online"),
                component: () => import("./pages/operation/sellout/index.vue"),
              },
              {
                path: "/weekly/carrier",
                name: "Carrier",
                meta: {
                  name: "Carrier",
                  display: false,
                  parentPath: "/weekly",
                  permissionName: "Weekly Sellout",
                },
                condition: () =>
                  userService.hasRoleName("Operation", "Carrier"),
                component: () => import("./pages/operation/sellout/index.vue"),
              },
            ],
          },
          {
            path: "/direct-shipment",
            name: "Direct-Shipment",
            meta: {
              name: "Direct Shipment",
              display: true,
            },
            component: () => import("./pages/DirectShipment/indexView.vue"),
            children: [
              {
                path: "/DirectShipment/Overview",
                name: "direct_shipment_overview",
                meta: {
                  name: "Overview",
                  display: true,
                  permissionName: "Direct Shipment",
                },
                condition: () => userService.hasPlatform("Direct Shipment"),
                component: () =>
                  import("./pages/DirectShipment/Overview/indexView.vue"),
                redirect: "/DirectShipment/Overview/Logistics",
                children: [
                  {
                    path: "/DirectShipment/Overview/Logistics",
                    name: "DS Logistics",
                    meta: {
                      name: "Direct Shipment",
                      parentPath: "/DirectShipment/Overview",
                      permissionName: "Direct Shipment",
                    },
                    condition: () => userService.hasPlatform("Direct Shipment"),
                    component: () =>
                      import(
                        "./pages/DirectShipment/Overview/Logistics/index.vue"
                      ),
                  },
                  {
                    path: "/DirectShipment/Overview/AgingDay",
                    name: "DS Aging Day",
                    meta: {
                      name: "Direct Shipment",
                      parentPath: "/DirectShipment/Overview",
                      permissionName: "Direct Shipment",
                    },
                    condition: () => userService.hasPlatform("Direct Shipment"),
                    component: () =>
                      import(
                        "./pages/DirectShipment/Overview/AgingDay/index.vue"
                      ),
                  },
                ],
              },
              {
                path: "/DirectShipment/Abnomality",
                name: "direct_shipment_abnomality",
                meta: {
                  name: "Abnomality",
                  display: true,
                  permissionName: "Direct Shipment",
                },
                condition: () => userService.hasPlatform("Direct Shipment"),
                component: () =>
                  import("./pages/DirectShipment/Abnomality/index.vue"),
              },
            ],
          },
          ...DASHBOARD_FAUCET,
          RESOURCE_PLANNING_ROUTER_MODULE,
          {
            path: "/video/list",
            name: "video",
            meta: {
              name: "Video",
              onlyShowParent: true,
              display: true,
              permissionName: "Video",
            },
            component: () => import("./pages/Implementation/Video/index.vue"),
            condition: () => userService.hasPlatform("Video"),
            children: [
              {
                path: "/video/details",
                name: "details",
                meta: {
                  name: "details",
                  display: false,
                  parentPath: "/video/list",
                  permissionName: "Video",
                },
                condition: () => userService.hasPlatform("Video"),
                component: () =>
                  import("./pages/Implementation/Video/Detail/index.vue"),
              },
              {
                path: "/video/list",
                name: "Video",
                meta: {
                  name: "Video",
                  display: false,
                  parentPath: "/video/list",
                  permissionName: "Video",
                },
                condition: () => userService.hasPlatform("Video"),
                component: () =>
                  import("./pages/Implementation/Video/List/index.vue"),
              },
              {
                path: "/video/search",
                name: "filter",
                meta: {
                  name: "search",
                  display: false,
                  parentPath: "/video/list",
                  permissionName: "Video",
                },
                condition: () => userService.hasPlatform("Video"),
                component: () =>
                  import("./pages/Implementation/Video/Search/index.vue"),
              },
              {
                path: "/video/mostview",
                name: "mostview",
                meta: {
                  name: "mostview",
                  display: false,
                  parentPath: "/video/list",
                  permissionName: "Video",
                },
                condition: () => userService.hasPlatform("Video"),
                component: () =>
                  import("./pages/Implementation/Video/MostView/index.vue"),
              },
              {
                path: "/video/list/recentlyadded",
                name: "recentlyadded",
                meta: {
                  name: "recentlyadded",
                  display: false,
                  parentPath: "/video/list",
                  permissionName: "Video",
                },
                condition: () => userService.hasPlatform("Video"),
                component: () =>
                  import(
                    "./pages/Implementation/Video/RecentlyAddedView/index.vue"
                  ),
              },
              {
                path: "/video/list/recentlywatched",
                name: "recentlywatched",
                meta: {
                  name: "recentlywatched",
                  display: false,
                  parentPath: "/video/list",
                  permissionName: "Video",
                },
                condition: () => userService.hasPlatform("Video"),
                component: () =>
                  import("./pages/Implementation/Video/RecentlyWatchedView"),
              },
              {
                path: "/video/upload",
                name: "upload",
                meta: {
                  name: "upload",
                  display: false,
                  parentPath: "/video/list",
                  permissionName: "Video",
                },
                condition: () => userService.hasPlatform("Video"),
                component: () =>
                  import("./pages/Implementation/Video/Upload/index.vue"),
              },
              {
                path: "/video/myvideo",
                name: "myvideo",
                meta: {
                  name: "myvideo",
                  display: false,
                  parentPath: "/video/list",
                  permissionName: "Video",
                },
                condition: () => userService.hasPlatform("Video"),
                component: () =>
                  import("./pages/Implementation/Video/MyVideo/index.vue"),
              },
              {
                path: "/video/audit",
                name: "audit",
                meta: {
                  name: "audit",
                  display: false,
                  parentPath: "/video/list",
                  permissionName: "Video",
                },
                condition: () => userService.hasPlatform("Video"),
                component: () =>
                  import("./pages/Implementation/Video/audit/index.vue"),
              },
            ],
          },
          {
            path: "/performance_review",
            name: "PerformanceReview",
            component: () => import("./pages/PerformanceReview/indexView.vue"),
            meta: {
              name: "Performance Review",
              alias: "Team Work Dashboard",
              display: true,
              permissionName: "Performance Review",
            },
            children: [
              {
                path: "/team_performance",
                name: "team_performance",
                meta: {
                  name: "Team Performance",
                  alias: "Team Dashboard",
                  display: true,
                  permissionName: "Performance Review",
                },
                condition: () =>
                  userService.hasRoleName("Performance Review", "Admin") ||
                  userService.hasRoleName(
                    "Performance Review",
                    "Sales Leader"
                  ) ||
                  userService.hasRoleName(
                    "Performance Review",
                    "Sales Manager"
                  ),
                component: () =>
                  import(
                    "./pages/PerformanceReview/TeamPerformance/indexView.vue"
                  ),
              },
              {
                path: "/performance_review_detail",
                name: "performance_review_detail",
                meta: {
                  name: "Performance Review Detail",
                  parentPath: "/team_performance",
                  display: false,
                  permissionName: "Performance Review",
                },
                condition: () => true,
                component: () =>
                  import(
                    "./pages/PerformanceReview/TeamPerformanceDetail/indexView.vue"
                  ),
              },
              {
                path: "/my_performance",
                name: "my_performance",
                meta: {
                  name: "My Performance",
                  alias: "My Dashboard",
                  display: true,
                  permissionName: "Performance Review",
                },
                condition: () =>
                  userService.hasRoleName("Performance Review", "Expert") ||
                  userService.hasRoleName(
                    "Performance Review",
                    "Sales Manager"
                  ),
                component: () =>
                  import(
                    "./pages/PerformanceReview/TeamPerformanceDetail/indexView.vue"
                  ),
              },
            ],
          },
          {
            path: "/milestone",
            name: "milestone",
            meta: {
              name: "Milestone",
              display: true,
              onlyShowParent: true,
              permissionName: "Milestone",
            },
            condition: () =>
              // userService.hasPlatform("Management") ||
              // userService.hasPlatform("Collaboration") ||
              userService.hasPlatform("Milestone"),
            // condition: () => (userService.hasPlatform("Management") || userService.hasRoleName("Video", "RTM Admin")) || userService.hasPlatform("Collaboration"),
            component: () => import("./pages/Management/Milestone2.vue"),
          },
          {
            path: "/system-usage",
            name: "System Usage",
            meta: {
              name: "System Usage",
              display: true,
            },
            component: RouterView,
            children: [
              // {
              //   path: "/system-usage/mystore-tracking",
              //   name: "SystemUsageMyStoreTracking",
              //   meta: {
              //     name: "MyStore",
              //     key: "mystore",
              //     display: true,
              //   },
              //   condition: () => {
              //     return (
              //       userService.hasPlatform("MyStore Tracking") &&
              //       (userService.hasRoleName(
              //         "MyStore Tracking Mono",
              //         "Permission"
              //       ) ||
              //         userService.hasPlatform("MyStore Tracking Carrier") ||
              //         userService.hasPlatform("MyStore Tracking Multi"))
              //     );
              //   },
              //   component: () =>
              //     import("./pages/Implementation/Tracking/MyStoreTracking.vue"),
              // },
              // {
              //   path: "/system-usage/mystore-tracking-mono",
              //   name: "SystemUsageMyStoreTrackingMono",
              //   meta: {
              //     name: "MyStore (Mono)",
              //     key: "mystore",
              //     display: true,
              //   },
              //   condition: () => {
              //     return (
              //       userService.hasPlatform("MyStore Tracking") &&
              //       (userService.hasRoleName(
              //         "MyStore Tracking Mono",
              //         "Business"
              //       ) ||
              //         userService.hasRoleName(
              //           "MyStore Tracking Mono",
              //           "Product"
              //         ))
              //     );
              //   },
              //   component: () =>
              //     import(
              //       "./pages/Implementation/Tracking/MyStoreTrackingMono.vue"
              //     ),
              // },
              // {
              //   path: "/mystore-tracking",
              //   name: "MyStoreTracking",
              //   meta: {
              //     name: "MyStore",
              //     key: "mystore",
              //     display: true,
              //   },
              //   condition: () => userService.hasPlatform("MyStore Tracking"),
              //   component: () =>
              //     import("./pages/Implementation/Tracking/MyStoreTracking1.vue"),
              // },
              // {
              //   path: "/login",
              //   name: "login",
              //   meta: {
              //     name: "login",
              //     key: "mystore",
              //     display: true,
              //   },
              //   condition: () => userService.hasPlatform("MyStore Tracking"),
              //   component: () =>
              //     import("./pages/Implementation/Tracking/login1111.vue"),
              // },
              // {
              //   path: "/login2",
              //   name: "login2",
              //   meta: {
              //     name: "login2",
              //     key: "mystore2",
              //     display: true,
              //   },
              //   condition: () => userService.hasPlatform("MyStore Tracking"),
              //   component: () =>
              //     import("./pages/Implementation/Tracking/login222.vue"),
              // },
              // {
              //   path: "/login2",
              //   name: "login3",
              //   meta: {
              //     name: "login3",
              //     key: "mystore3",
              //     display: true,
              //   },
              //   condition: () => userService.hasPlatform("MyStore Tracking"),
              //   component: () =>
              //     import("./pages/Implementation/Tracking/login222.vue"),
              // },
              // {
              //   path: "/homepage",
              //   name: "homepage",
              //   meta: {
              //     name: "homepage",
              //     key: "homepage",
              //     display: true,
              //   },
              //   condition: () => userService.hasPlatform("MyStore Tracking"),
              //   component: () =>
              //     import("./pages/Implementation/Tracking/homepage.vue"),
              // },
              // {
              //   path: "/mybusiness-tracking",
              //   name: "MyBusinessTracking",
              //   meta: {
              //     name: "MyBusiness",
              //     key: "mybusiness",
              //     display: true,
              //   },
              //   condition: () => userService.hasPlatform("MyBusiness Tracking"),
              //   component: () =>
              //     import(
              //       "./pages/Implementation/Tracking/MyBusinessTracking.vue"
              //     ),
              // },
            ],
          },
        ],
      },
      {
        path: "/index-analysis",
        name: "Analysis",
        meta: {
          name: "Analysis",
          display: true,
        },
        component: RouterView,
        children: [
          // {
          //   path: "/ai-search",
          //   name: "AI Search",
          //   meta: {
          //     tag: "DAIS",
          //     name: "AI Search",
          //     display: true,
          //     permissionName: "DAIS",
          //   },
          //   component: RouterView,
          //   children: [
          //     {
          //       path: "/dais",
          //       name: "Dais",
          //       meta: {
          //         name: "DAIS",
          //         display: true,
          //         permissionName: "DAIS",
          //       },
          //       condition: () => userService.hasPlatform("Q&A"),
          //       component: () => import("./pages/Dais/DaisIndex.vue"),
          //     },
          //     {
          //       path: "/dais/chat",
          //       name: "ChatIndex",
          //       meta: {
          //         name: "ChatIndex",
          //         parentPath: "/dais",
          //         permissionName: "DAIS",
          //       },
          //       condition: () => userService.hasPlatform("Q&A"),
          //       component: () => import("./pages/Dais/Chat/Index.vue"),
          //     },
          //   ],
          // },
          // {
          //   path: "/city/insights",
          //   name: "insights",
          //   meta: {
          //     name: "City Insights",
          //     display: true,
          //     onlyShowParent: true,
          //     permissionName: "City Insights",
          //   },
          //   // redirect: to => { return { path: '/city/insights' }},
          //   condition: () => {
          //     return userService.hasPlatform("City Insights");
          //   },
          //   component: () => import("./pages/operation/cityInsights/index.vue"),
          //   children: [
          //     {
          //       path: "/city/details",
          //       name: "details",
          //       meta: {
          //         name: "details",
          //         parentPath: "/city/insights",
          //         display: false,
          //         permissionName: "City Insights",
          //       },
          //       // condition: () => userService.hasPlatform("Video"),
          //       component: () =>
          //         import("./pages/operation/cityInsights/Details/index.vue"),
          //     },
          //     {
          //       path: "/city/districtDetails",
          //       name: "districtDetails",
          //       meta: {
          //         name: "districtDetails",
          //         parentPath: "/city/insights",
          //         display: false,
          //         permissionName: "City Insights",
          //       },
          //       // condition: () => userService.hasPlatform("Video"),
          //       component: () =>
          //         import("./pages/operation/cityInsights/Details/index.vue"),
          //     },
          //     {
          //       path: "/city/insights",
          //       name: "insights",
          //       meta: {
          //         name: "insights",
          //         parentPath: "/city/insights",
          //         display: false,
          //         permissionName: "City Insights",
          //       },
          //       // condition: () => userService.hasPlatform("Video"),
          //       component: () =>
          //         import("./pages/operation/cityInsights/Insights/index.vue"),
          //     },
          //     {
          //       path: "/city/districtInsights",
          //       name: "districtInsights",
          //       meta: {
          //         name: "districtInsights",
          //         parentPath: "/city/insights",
          //         display: false,
          //         permissionName: "City Insights",
          //       },
          //       // condition: () => userService.hasPlatform("Video"),
          //       component: () =>
          //         import("./pages/operation/cityInsights/Insights/index.vue"),
          //     },
          //   ],
          // },
          RewardSystem,
          ChannelCompliance,
          {
            path: "/store-finder",
            name: "Store Finder",
            meta: {
              name: "Store Finder",
              display: true,
            },
            condition: () => userService.hasPlatform("Store Finder"),
            component: () => RouterView,
            children: [
              {
                path: "/store-finder",
                name: "store_finder",
                meta: {
                  name: "Store Finder",
                  display: true,
                  onlyShowParent: true,
                  permissionName: "Store Finder",
                },
                condition: () =>
                  userService.hasPlatform("Store Finder") &&
                  userService.hasRoleName("Store Finder", "Mall List"),
                component: () =>
                  import("./pages/operation/StoreFinder/IndexView.vue"),
              },
              {
                path: "/store-finder/detail",
                name: "ShoppingMallDetail",
                meta: {
                  name: "ShoppingMallDetail",
                  parentPath: "/store-finder",
                  permissionName: "Store Finder",
                  display: false,
                },
                component: () =>
                  import(
                    "./pages/operation/StoreFinder/detail/ShoppingMallDetail.vue"
                  ),
              },
              {
                path: "/settings",
                name: "Settings",
                meta: {
                  name: "Settings",
                  display: true,
                  onlyShowParent: true,
                  permissionName: "Store Finder",
                },
                condition: () =>
                  userService.hasPlatform("Store Finder") &&
                  userService.hasRoleName("Store Finder", "Settings"),
                component: () =>
                  import("./pages/operation/StoreFinder/SettingsView.vue"),
              },
            ],
          },
          {
            path: "/power_box",
            name: "DG Planning",
            meta: {
              name: "DG Planning",
              alias: "Power Box",
              tag: "Power Box",
              display: true,
              permissionName: "Power Box",
            },
            component: () => import("./pages/PowerBox/indexView.vue"),
            children: [
              {
                path: "/power_box/dashboard",
                name: "power_box_dashboard",
                meta: {
                  name: "AI Solver",
                  display: true,
                  permissionName: "Power Box",
                },
                condition: () =>
                  (userService.hasPlatform("Power Box") &&
                    (userService.hasRoleName(
                      "Power Box",
                      "AI Recommendation"
                    ) ||
                      userService.hasRoleName("Power Box", "AI Simulation") ||
                      userService.hasRoleName(
                        "Power Box",
                        "AI Simulation 2.0"
                      ))) ||
                  (userService.hasPlatform("Power Box Mono") &&
                    (userService.hasRoleName(
                      "Power Box Mono",
                      "AI Recommendation"
                    ) ||
                      userService.hasRoleName(
                        "Power Box Mono",
                        "AI Simulation"
                      ))) ||
                  (userService.hasPlatform("Power Box Online") &&
                    (userService.hasRoleName(
                      "Power Box Online",
                      "AI Recommendation"
                    ) ||
                      userService.hasRoleName(
                        "Power Box Online",
                        "AI Simulation"
                      ))),
                component: () =>
                  import("./pages/PowerBox/Dashboard/indexView.vue"),
              },
              {
                path: "/power_box/tool",
                name: "power_box_tool",
                meta: {
                  name: "power_box_tool",
                  parentPath: "/power_box/dashboard",
                  display: false,
                  permissionName: "Power Box",
                },
                condition: () => userService.hasPlatform("Power Box"),
                component: () => import("./pages/PowerBox/Tool/indexView.vue"),
              },
              {
                path: "/power_box/tool_mono",
                name: "power_box_tool_mono",
                meta: {
                  name: "power_box_tool_mono",
                  parentPath: "/power_box/dashboard",
                  display: false,
                  permissionName: "Power Box",
                },
                condition: () => userService.hasPlatform("Power Box"),
                component: () =>
                  import("./pages/PowerBox/ToolMono/indexView.vue"),
              },
              {
                path: "/power_box/tool_online",
                name: "power_box_tool_online",
                meta: {
                  name: "power_box_tool_online",
                  parentPath: "/power_box/dashboard",
                  display: false,
                  permissionName: "Power Box",
                },
                condition: () => userService.hasPlatform("Power Box"),
                component: () =>
                  import("./pages/PowerBox/ToolOnline/indexView.vue"),
              },
              {
                path: "/power_box/simulation",
                name: "power_box_simulation",
                meta: {
                  name: "power_box_simulation",
                  parentPath: "/power_box/dashboard",
                  display: false,
                  permissionName: "Power Box",
                },
                condition: () => userService.hasPlatform("Power Box"),
                component: () =>
                  import("./pages/PowerBox/Simulation/indexView.vue"),
              },
              {
                path: "/power_box/simulation_mono",
                name: "power_box_simulation_mono",
                meta: {
                  name: "power_box_simulation_mono",
                  parentPath: "/power_box/dashboard",
                  display: false,
                  permissionName: "Power Box",
                },
                condition: () => userService.hasPlatform("Power Box"),
                component: () =>
                  import("./pages/PowerBox/SimulationMono/indexView.vue"),
              },
              {
                path: "/power_box/simulation_online",
                name: "power_box_simulation_online",
                meta: {
                  name: "power_box_simulation_online",
                  parentPath: "/power_box/dashboard",
                  display: false,
                  permissionName: "Power Box",
                },
                condition: () => userService.hasPlatform("Power Box"),
                component: () =>
                  import("./pages/PowerBox/SimulationOnline/indexView.vue"),
              },
              {
                path: "/power_box/knowledge_pool",
                name: "power_box_knowledge_pool",
                meta: {
                  name: "Knowledge Pool",
                  display: true,
                  permissionName: "Power Box",
                },
                condition: () =>
                  userService.hasRoleName("Power Box", "Knowledge Pool"),
                component: () => import("./pages/PowerBox/KnowledgePool"),
              },
              {
                path: "/power_box/knowledge_pool/detail",
                name: "power_box_knowledge_pool",
                meta: {
                  name: "Knowledge Pool detail",
                  parentPath: "/power_box/knowledge_pool",
                  display: false,
                  permissionName: "Power Box",
                },
                condition: () =>
                  userService.hasRoleName("Power Box", "Knowledge Pool"),
                component: () =>
                  import("./pages/PowerBox/KnowledgePool/detail"),
              },
              {
                path: "/power_box/datasource",
                name: "power_box_datasource",
                meta: {
                  name: "Data Source",
                  display: true,
                  permissionName: "Power Box",
                },
                condition: () =>
                  userService.hasRoleName("Power Box", "Data Source"),
                component: () =>
                  import("./pages/PowerBox/DataSource/indexView.vue"),
              },
              {
                path: "/power_box/simulation_v2",
                name: "power_box_simulation_v2",
                meta: {
                  name: "power_box_simulation_v2",
                  parentPath: "/power_box/dashboard",
                  display: false,
                  permissionName: "Power Box",
                },
                condition: () =>
                  userService.hasRoleName("Power Box", "AI Simulation 2.0"),
                component: () =>
                  import("./pages/PowerBox/SimulationV2/indexView.vue"),
              },
            ],
          },
          FORECAST_PLATFORM,
          HITL_ROUTER_MODULE,
          {
            path: "/ailabhome",
            name: "Ailabhome",
            meta: {
              name: "AI Lab",
              onlyShowParent: true,
              display: true,
              permissionName: "AI Lab",
            },
            condition: () => {
              return userService.hasPlatform("AI Lab");
            },
            component: () => RouterView,
            children: [
              {
                path: "/ailabhome",
                name: "Ailabhome",
                meta: {
                  parentPath: "/ailabhome",
                  permissionName: "AI Lab",
                },
                condition: () => {
                  return userService.hasPlatform("AI Lab");
                },
                component: () => import("./pages/tool/ailab/ailabindex.vue"),
              },
              {
                path: "/ailabmodel/:id",
                name: "ailabmodel",
                meta: {
                  parentPath: "/ailabhome",
                  permissionName: "AI Lab",
                },
                condition: () => {
                  return userService.hasPlatform("AI Lab");
                },
                component: () => import("./pages/tool/ailab/ailabmodel.vue"),
              },
              {
                path: "/ailabcreate",
                name: "ailabcreate",
                meta: {
                  parentPath: "/ailabhome",
                  permissionName: "AI Lab",
                },
                condition: () => {
                  return userService.hasPlatform("AI Lab");
                },
                component: () => import("./pages/tool/ailab/ailabcreate.vue"),
              },
            ],
          },
          AIProgram,
          {
            path: "/anti_fraud",
            name: "Anti-Fraud Screening",
            meta: {
              name: "Anti-Fraud Screening",
              display: true,
            },
            component: () => import("./pages/AntiFraud/indexView.vue"),
            children: [
              {
                path: "/AntiFraud/Summary",
                name: "anti_fraud_summary",
                meta: {
                  name: "Summary",
                  display: true,
                  permissionName: "Channel Anti-Fraud",
                },
                condition: () => {
                  return userService.hasPlatform("Channel Anti-Fraud");
                },
                component: () => import("./pages/AntiFraud/Summary/index.vue"),
              },
              {
                path: "/AntiFraud/Details",
                name: "anti_fraud_Details",
                meta: {
                  name: "Details",
                  display: true,
                  permissionName: "Channel Anti-Fraud",
                },
                condition: () => {
                  return userService.hasPlatform("Channel Anti-Fraud");
                },
                component: () => import("./pages/AntiFraud/Details/index.vue"),
              },
            ],
          },
          LLMPlayground,
          // {
          //   path: "/store-finder",
          //   name: "store_finder",
          //   meta: {
          //     name: "Store Finder",
          //     display: true,
          //     onlyShowParent: true,
          //     permissionName: "Store Finder",
          //   },
          //   condition: () => true,
          //   children: [
          //     {
          //       path: "/store-finder",
          //       name: "store_finder",
          //       meta: {
          //         name: "Store Finder",
          //         display: true,
          //         onlyShowParent: true,
          //         permissionName: "Store Finder",
          //       },
          //       // condition: () => userService.hasPlatform("Forecast Accuracy"),
          //       component: () =>
          //         import("./pages/operation/StoreFinder/IndexView.vue"),
          //     },
          //     {
          //       path: "/store-finder",
          //       name: "store_finder",
          //       meta: {
          //         name: "Store Finder2",
          //         display: true,
          //         onlyShowParent: true,
          //         permissionName: "Store Finder",
          //       },
          //       // condition: () => userService.hasPlatform("Forecast Accuracy"),
          //       component: () =>
          //         import("./pages/operation/StoreFinder/IndexView.vue"),
          //     },
          //     {
          //       path: "/store-finder/detail",
          //       name: "ShoppingMallDetail",
          //       meta: {
          //         name: "ShoppingMallDetail",
          //         parentPath: "/store-finder",
          //         permissionName: "Store Finder",
          //       },
          //       component: () =>
          //         import(
          //           "./pages/operation/StoreFinder/detail/ShoppingMallDetail.vue"
          //         ),
          //     },
          //   ],
          //   component: () => import("./pages/operation/StoreFinder/index.vue"),
          // },
        ],
      },
      {
        path: "/index-operation",
        name: "Operation",
        meta: {
          name: "Operation",
          display: true,
        },
        component: RouterView,
        children: [
          {
            path: "/tool",
            name: "Tool",
            component: () => import("./pages/tool/index.vue"),
            meta: {
              name: "Experiment",
              display: true,
            },
            children: [
              // {
              //   path: "/tentative",
              //   name: "Quasi",
              //   meta: {
              //     name: "Quasi Experiment",
              //     display: true,
              //   },
              //   condition: () => userService.hasPlatform("AB Test"),
              //   component: () => import("./pages/tool/abtest/tentative.vue"),
              // },
              ...STD_EXP_ROUTES,
              ...CUSTOMIZED_EXP_ROUTES,
              ...STD_MULTI_EXP_ROUTES,
              ...STD_CARRIER_EXP_ROUTES,
              ...CST_ONLINE_ROUTES,
              ...STD_MONO_EXP_ROUTES,
              ...STD_COMMON_ONLINE_EXP_ROUTES,
              ...INITIATIVES_TRACKING,
              ...EXP_SUMMARY_ROUTES,
            ],
          },
          // {
          //   path: "/front_exp",
          //   name: "Experiment-Vue3",
          //   meta: {
          //     name: "Experiment-Vue3",
          //     display: () => true,
          //   },
          //   condition: () => true,
          //   component: () =>
          //     import("./pages/MicroModules/modules/Experiment/index.vue"),
          //   children: [
          //     {
          //       path: "/front_exp/experiments",
          //       name: "experimentsStandardHub",
          //       meta: {
          //         name: "AB Test Vue3",
          //         display: true,
          //       },
          //       condition: () => userService.hasPlatform("AB Test"),
          //       component: () =>
          //         import("./pages/MicroModules/modules/Experiment/index.vue"),
          //     },
          //     {
          //       path: "/experiment/customized/tracking/",
          //       name: "Initiatives Tracking",
          //       meta: {
          //         name: "Initiatives Tracking",
          //         display: true,
          //       },
          //       condition: () =>
          //         userService.hasPlatform("Initiatives Tracking"),
          //       component: () =>
          //         import("./pages/Experiment/InitiativesTracking/index.vue"),
          //     },
          //     {
          //       path: "*",
          //       name: "Experiment-Micro",
          //       component: () =>
          //         import("./pages/MicroModules/modules/Experiment/index.vue"),
          //     },
          //   ],
          // },
          STAMP_ROUTES,
          {
            path: "/fast",
            name: "FAST",
            component: () => import("./pages/Fast/Allocation/index.vue"),
            meta: {
              name: "FAST",
              // tag: "FAST",
              display: true,
              permissionName: "FAST",
            },
            children: [
              /* 
                Forecast Demand start
               */
              ...FAST_STANDER_ROUTER,
              // ...FORECAST_DEMAND_ROUTER,

              ...NPIAllocationRouter,
              /* 
              Forecast start
               */
              {
                path: "/forecast-lite",
                name: "ML Forecast",
                meta: {
                  name: "ML Forecast",
                  parentPath: "/fd-e2e",
                  display: false,
                  permissionName: "FAST",
                },
                condition: () =>
                  // userService.hasPlatform("FAST CP&F") ||
                  userService.hasPlatform("FAST Mono"),
                // userService.hasPlatform("FAST Multi") ||
                // userService.hasPlatform("FAST Carrier") ||
                // userService.hasPlatform("FAST Online"),
                component: () =>
                  import("./pages/Fast/LiteModule/Forecast/index.vue"),
              },
              {
                path: "/forecast-lite-detail",
                name: "ML Forecast",
                meta: {
                  name: "ML Forecast",
                  parentPath: "/fd-e2e",
                  display: false,
                  permissionName: "FAST",
                },
                condition: () =>
                  // userService.hasPlatform("FAST CP&F") ||
                  userService.hasPlatform("FAST Mono"),
                // userService.hasPlatform("FAST Multi") ||
                // userService.hasPlatform("FAST Carrier") ||
                // userService.hasPlatform("FAST Online"),
                component: () =>
                  import("./pages/Fast/LiteModule/Forecast/DetailMono.vue"),
              },
              {
                path: "/forecast-lite/detail-cpf",
                name: "ForecastDetail",
                meta: {
                  name: "Forecast",
                  parentPath: "/forecast-lite",
                  display: false,
                  permissionName: "FAST",
                },
                condition: () => userService.hasPlatform("FAST CP&F"),
                component: () =>
                  import("./pages/Fast/LiteModule/Forecast/DetailCpf.vue"),
              },
              {
                path: "/forecast-lite/detail-multi",
                name: "ForecastDetail",
                meta: {
                  name: "Forecast",
                  parentPath: "/forecast-lite",
                  display: false,
                  permissionName: "FAST",
                },
                condition: () =>
                  userService.hasPlatform("FAST Multi") ||
                  userService.hasPlatform("FAST Carrier") ||
                  userService.hasPlatform("FAST Online"),
                component: () =>
                  import("./pages/Fast/LiteModule/Forecast/DetailMulti.vue"),
              },
              /* 
              Forecast end
               */
              /* 
              Demand start
               */
              // {
              //   path: "/demandAll",
              //   name: "demandAll",
              //   meta: {
              //     name: "Demand",
              //     display: true,
              //     permissionName: "FAST",
              //   },
              //   condition: () => userService.hasAllChildMenu(ROUTER_DEMAND),
              //   redirect: () => {
              //     const newList = ROUTER_DEMAND.filter((item) =>
              //       item.hasPlatform()
              //     );
              //     if (newList.length > 0) {
              //       return { path: newList[0].path };
              //     }
              //   },
              // },
              // {
              //   path: "/demand-lite",
              //   name: "DemandLite",
              //   meta: {
              //     name: "Demand",
              //     parentPath: "/demandAll",
              //     display: false,
              //     permissionName: "FAST",
              //   },
              //   condition: () =>
              //     userService.hasPlatform("FAST CP&F") ||
              //     userService.hasPlatform("FAST Mono") ||
              //     userService.hasPlatform("FAST Multi") ||
              //     userService.hasPlatform("FAST Carrier") ||
              //     userService.hasPlatform("FAST Online"),
              //   component: () =>
              //     import("./pages/Fast/LiteModule/Demand/index.vue"),
              // },
              // {
              //   path: "/demand-lite/detail-cpf",
              //   name: "DemandDetail",
              //   meta: {
              //     name: "Demand",
              //     parentPath: "/demandAll",
              //     display: false,
              //     permissionName: "FAST",
              //   },
              //   condition: () => userService.hasPlatform("FAST CP&F"),
              //   component: () =>
              //     import("./pages/Fast/LiteModule/Demand/DetailCpf.vue"),
              // },
              // {
              //   path: "/ideal-demand",
              //   name: "Ideal Demand",
              //   meta: {
              //     name: "Ideal Demand",
              //     parentPath: "/demandAll",
              //     display: false,
              //     permissionName: "FAST",
              //   },
              //   condition: () =>
              //     userService.hasRoleName("FAST Mono", "Permission") ||
              //     userService.hasRoleName("FAST Multi", "Planning Team") ||
              //     userService.hasRoleName("FAST Carrier", "Planning Team") ||
              //     userService.hasRoleName("FAST Online", "Planning Team") ||
              //     userService.hasRoleName("FAST CP&F", "Permission") ||
              //     userService.hasRoleName("FAST Education", "Planning Team") ||
              //     userService.hasRoleName("FAST Enterprise", "Planning Team"),
              //   component: () => import("./pages/Fast/IdealDemand"),
              // },
              // {
              //   path: "/ideal-demand/detail",
              //   name: "Ideal Demand Detail",
              //   meta: {
              //     name: "ideal-demand-detail",
              //     parentPath: "/demandAll",
              //     display: false,
              //     permissionName: "FAST",
              //   },
              //   condition: () =>
              //     userService.hasRoleName("FAST Mono", "Permission") ||
              //     userService.hasRoleName("FAST Multi", "Planning Team") ||
              //     userService.hasRoleName("FAST Carrier", "Planning Team") ||
              //     userService.hasRoleName("FAST Online", "Planning Team") ||
              //     userService.hasRoleName("FAST CP&F", "Permission") ||
              //     userService.hasRoleName("FAST Education", "Planning Team") ||
              //     userService.hasRoleName("FAST Enterprise", "Planning Team"),
              //   component: () => import("./pages/Fast/IdealDemand/detail"),
              // },
              {
                path: "/fd-advance",
                name: "ForecastDemandAdvance",
                meta: {
                  name: "Advanced",
                  parentPath: "/fd-e2e",
                  display: false,
                  permissionName: "FAST",
                },
                condition: () => userService.hasPlatform("FAST Mono"),
                component: () => import("./pages/Fast/Advance/AdvanceHome.vue"),
              },
              {
                path: "/system-suggestion",
                name: "SystemSuggestion",
                meta: {
                  parentPath: "/fd-e2e",
                  display: false,
                  permissionName: "FAST",
                },
                condition: () => userService.hasPlatform("FAST Mono"),
                component: () =>
                  import("./pages/Fast/Advance/SystemSuggestion.vue"),
              },
              {
                path: "/demand-parameters",
                name: "DemandParameters",
                meta: {
                  parentPath: "/fd-e2e",
                  display: false,
                  permissionName: "FAST",
                },
                condition: () => userService.hasPlatform("FAST Mono"),
                component: () =>
                  import("./pages/Fast/Advance/DemandParameters.vue"),
              },
              {
                path: "/lob-select",
                name: "LobSelection",
                meta: {
                  parentPath: "/fd-e2e",
                  display: false,
                  permissionName: "FAST",
                },
                condition: () =>
                  userService.hasRoleName("FAST Mono", "Permission") ||
                  userService.hasRoleName("FAST Mono", "Account Interface"),
                component: () =>
                  import("./pages/Fast/Advance/LobSelection.vue"),
              },
              {
                path: "/adjustment-data/first",
                name: "ApprovalFirst",
                meta: {
                  parentPath: "/fd-e2e",
                  display: false,
                  permissionName: "FAST",
                },
                condition: () => userService.hasPlatform("FAST Mono"),
                component: () =>
                  import("./pages/Fast/Advance/ApprovalFirst.vue"),
              },
              {
                path: "/adjustment-data/second",
                name: "ApprovalSecond",
                meta: {
                  parentPath: "/fd-e2e",
                  display: false,
                  permissionName: "FAST",
                },
                condition: () => userService.hasPlatform("FAST Mono"),
                component: () =>
                  import("./pages/Fast/Advance/ApprovalSecond.vue"),
              },
              {
                path: "/rtm-summary",
                name: "RTMSummary",
                meta: {
                  parentPath: "/fd-e2e",
                  display: false,
                  permissionName: "FAST",
                },
                condition: () => userService.hasPlatform("FAST Mono"),
                component: () => import("./pages/Fast/Advance/RTMSummary.vue"),
              },
              {
                path: "/esr-variance",
                name: "EsrVariance",
                meta: {
                  parentPath: "/fd-e2e",
                  display: false,
                  permissionName: "FAST",
                },
                condition: () => userService.hasPlatform("FAST Mono"),
                component: () => import("./pages/Fast/Advance/EsrVariance.vue"),
              },
              // {
              //   path: "/npp-supplier",
              //   name: "NppSupplier",
              //   meta: {
              //     parentPath: "/demandAll",
              //     display: false,
              //   },
              //   condition: () => userService.hasPlatform("FAST Mono"),
              //   component: () => import("./pages/Fast/Advance/NppSupplier.vue"),
              // },
              {
                path: "/demand-lite/detail-multi",
                name: "DemandDetail",
                meta: {
                  name: "Demand",
                  parentPath: "/fd-e2e",
                  display: false,
                  permissionName: "FAST",
                },
                condition: () =>
                  userService.hasPlatform("FAST Multi") ||
                  userService.hasPlatform("FAST Carrier") ||
                  userService.hasPlatform("FAST Online"),
                component: () =>
                  import("./pages/Fast/LiteModule/Demand/DetailMulti.vue"),
              },
              {
                path: "/rtm-forecast",
                name: "RtmForecast",
                meta: {
                  name: "Advanced",
                  parentPath: "/fd-e2e",
                  display: false,
                  permissionName: "FAST",
                },
                condition: () => userService.hasPlatform("FAST Mono"),
                component: () =>
                  import(
                    "./pages/Fast/LiteModule/Forecast/RTMForecast/list.vue"
                  ),
              },
              {
                path: "/rtm-forecast-detail",
                name: "RtmForecastDetail",
                meta: {
                  name: "Advanced",
                  parentPath: "/fd-e2e",
                  display: false,
                  permissionName: "FAST",
                },
                condition: () => userService.hasPlatform("FAST Mono"),
                component: () =>
                  import(
                    "./pages/Fast/LiteModule/Forecast/RTMForecast/detail.vue"
                  ),
              },
              /* 
              Demand end
               */
              /* 
              Allocation start
               */
              {
                path: "/allocationAll",
                name: "Allocation",
                meta: {
                  name: "Allocation",
                  display: true,
                  permissionName: "FAST",
                },
                condition: () => userService.hasAllChildMenu(ROUTER_ALLOCATION),
                redirect: () => {
                  const newList = ROUTER_ALLOCATION.filter((item) =>
                    item.hasPlatform()
                  );

                  if (newList.length > 0) {
                    if (
                      newList[0].name === CHANNEL_ALLOCATION &&
                      newList[0].localRtm()[0].label === CPF
                    ) {
                      return "/cpf-allocation";
                    } else {
                      return { path: newList[0].path };
                    }
                  }
                },
              },
              {
                path: "/cpf-allocation",
                name: "Allocation",
                meta: {
                  parentPath: "/allocationAll",
                  name: "Channel Allocation",
                  display: false,
                  permissionName: "FAST",
                },
                condition: () =>
                  userService.hasRoleName("FAST CP&F", "Permission"),
                component: () => import("./pages/Fast/CpfAllocation/CpfIndex"),
              },
              {
                path: "/cpf-allocation/detail",
                name: "CpfAllocationDetail",
                meta: {
                  name: "Allocation",
                  parentPath: "/allocationAll",
                  display: false,
                  permissionName: "FAST",
                },
                condition: () =>
                  userService.hasRoleName("FAST CP&F", "Permission"),
                component: () =>
                  import("./pages/Fast/CpfAllocation/CpfAllocationDetail"),
              },
              {
                path: "/prep-allocation",
                name: "PrepAllocation",
                meta: {
                  name: "Allocation Preparation",
                  parentPath: "/allocationAll",
                  display: false,
                  permissionName: "FAST",
                },
                condition: () =>
                  userService.hasRoleName("FAST Mono", "Permission") ||
                  userService.hasRoleName("FAST Multi", "Planning Team") ||
                  userService.hasRoleName("FAST Carrier", "Planning Team") ||
                  userService.hasRoleName("FAST Online", "Planning Team") ||
                  userService.hasRoleName("FAST Education", "Planning Team") ||
                  userService.hasRoleName("FAST Enterprise", "Planning Team") ||
                  userService.hasRoleName(
                    "FAST HK/TW Carrier",
                    "Planning Team"
                  ) ||
                  userService.hasRoleName("FAST HK/TW RP", "Planning Team") ||
                  userService.hasRoleName(
                    `${FAST} ${HK_TW_EDU}`,
                    ROLE_PLANNING_TEAM
                  ) ||
                  userService.hasRoleName(
                    `${FAST} ${HK_TW_ENT}`,
                    ROLE_PLANNING_TEAM
                  ),
                component: () => import("./pages/Fast/CpfAllocation/PrepIndex"),
              },
              {
                path: "/prep-allocation/detail",
                name: "PrepAllocationDetail",
                meta: {
                  name: "Allocation Prep. & Submission",
                  parentPath: "/allocationAll",
                  display: false,
                  permissionName: "FAST",
                },
                condition: () =>
                  userService.hasRoleName("FAST Mono", "Permission") ||
                  userService.hasRoleName("FAST Multi", "Planning Team") ||
                  userService.hasRoleName("FAST Carrier", "Planning Team") ||
                  userService.hasRoleName("FAST Online", "Planning Team") ||
                  userService.hasRoleName("FAST Education", "Planning Team") ||
                  userService.hasRoleName("FAST Enterprise", "Planning Team") ||
                  userService.hasRoleName(
                    "FAST HK/TW Carrier",
                    "Planning Team"
                  ) ||
                  userService.hasRoleName("FAST HK/TW RP", "Planning Team"),
                component: () =>
                  import("./pages/Fast/CpfAllocation/PrepAllocationDetail"),
              },
              {
                path: "/allocation-run",
                name: "AllocationRun",
                meta: {
                  name: "Allocation Run",
                  parentPath: "/allocationAll",
                  display: false,
                  permissionName: "FAST",
                },
                condition: () =>
                  userService.hasRoleName("FAST CP&F", "Permission"),
                component: () =>
                  import("./pages/Fast/CpfAllocation/AllocationRun/index.vue"),
              },
              {
                path: "/allocation-run/preview",
                name: "AllocationRunDetail",
                meta: {
                  name: "Allocation Run",
                  parentPath: "/allocationAll",
                  display: false,
                  permissionName: "FAST",
                },
                condition: () =>
                  userService.hasRoleName("FAST CP&F", "Permission"),
                component: () =>
                  import(
                    "./pages/Fast/CpfAllocation/AllocationRun/preview.vue"
                  ),
              },
              {
                path: "/allocation",
                name: "allocationList",
                meta: {
                  name: "Sold-to Allocation",
                  parentPath: "/allocationAll",
                  display: false,
                  permissionName: "FAST",
                },
                condition: () =>
                  userService.hasRoleName("FAST Mono", "Permission"),
                component: () =>
                  import("./pages/Fast/Allocation/allocationList.vue"),
              },
              {
                path: "/editAllocation",
                name: "edit_allocation",
                meta: {
                  parentPath: "/allocationAll",
                  permissionName: "FAST",
                },
                condition: () =>
                  userService.hasRoleName("FAST Mono", "Permission"),
                component: () =>
                  import("./pages/Fast/Allocation/edit_allocation.vue"),
              },
              {
                path: "/viewAllocation",
                name: "view_allocation",
                meta: {
                  parentPath: "/allocationAll",
                  permissionName: "FAST",
                },
                condition: () =>
                  userService.hasRoleName("FAST Mono", "Permission"),
                component: () =>
                  import("./pages/Fast/Allocation/view_allocation.vue"),
              },
              {
                path: "/resultAllocation/:id",
                name: "result_allocation",
                meta: {
                  parentPath: "/allocationAll",
                  permissionName: "FAST",
                },
                condition: () =>
                  userService.hasRoleName("FAST Mono", "Permission"),
                component: () =>
                  import("./pages/Fast/Allocation/result_allocation.vue"),
              },
              {
                path: "/posList",
                name: "posList",
                meta: {
                  name: "POS Allocation",
                  parentPath: "/allocationAll",
                  display: false,
                  permissionName: "FAST",
                },
                condition: () => userService.hasPlatform("FAST Mono"),
                component: () => import("./pages/Fast/Pos/posList.vue"),
              },
              {
                path: "/npiSku",
                name: "npiSku",
                meta: {
                  parentPath: "/allocationAll",
                  permissionName: "FAST",
                },
                condition: () =>
                  userService.hasRoleName("FAST Mono", "Permission"),
                component: () =>
                  import("./pages/Fast/Pos/createPosAllocation/npiSku.vue"),
              },
              {
                path: "/sustainingSku",
                name: "sustainingSku",
                meta: {
                  parentPath: "/allocationAll",
                  permissionName: "FAST",
                },
                condition: () =>
                  userService.hasRoleName("FAST Mono", "Permission"),
                component: () =>
                  import(
                    "./pages/Fast/Pos/createPosAllocation/sustainingSku.vue"
                  ),
              },
              {
                path: "/posResult",
                name: "posResult",
                meta: {
                  parentPath: "/allocationAll",
                  permissionName: "FAST",
                },
                condition: () => userService.hasPlatform("FAST Mono"),
                component: () => import("./pages/Fast/Pos/posResult.vue"),
              },
              {
                path: "/supplyInfo",
                name: "supplyInfo",
                meta: {
                  parentPath: "/allocationAll",
                  permissionName: "FAST",
                },
                condition: () =>
                  userService.hasRoleName("FAST Mono", "Permission"),
                component: () =>
                  import("./pages/Fast/Pos/createPosAllocation/supplyInfo.vue"),
              },
              {
                path: "/stopAllocation",
                name: "stopAllocation",
                meta: {
                  parentPath: "/allocationAll",
                  permissionName: "FAST",
                },
                condition: () =>
                  userService.hasRoleName("FAST Mono", "Permission"),
                component: () =>
                  import(
                    "./pages/Fast/Pos/createPosAllocation/stopAllocation.vue"
                  ),
              },
              {
                path: "/fixedSku",
                name: "fixedSku",
                meta: {
                  parentPath: "/allocationAll",
                  permissionName: "FAST",
                },
                condition: () =>
                  userService.hasRoleName("FAST Mono", "Permission"),
                component: () =>
                  import("./pages/Fast/Pos/createPosAllocation/fixedSku.vue"),
              },
              {
                path: "/nppPlan",
                name: "nppPlan",
                meta: {
                  parentPath: "/allocationAll",
                  permissionName: "FAST",
                },
                condition: () =>
                  userService.hasRoleName("FAST Mono", "Permission"),
                component: () =>
                  import("./pages/Fast/Pos/createPosAllocation/nppPlan.vue"),
              },
              {
                path: "/nppSelfSupply",
                name: "nppSelfSupply",
                meta: {
                  parentPath: "/allocationAll",
                  permissionName: "FAST",
                },
                condition: () =>
                  userService.hasRoleName("FAST Mono", "Permission"),
                component: () =>
                  import(
                    "./pages/Fast/Pos/createPosAllocation/nppSelfSupply.vue"
                  ),
              },
              /* 
              Allocation end
               */

              // {
              //   path: "/secondaryallocation",
              //   name: "Secondary Allocation",
              //   meta: {
              //     name: "Secondary Allocation",
              //     display: true,
              //   },
              //   condition: () => userService.hasPlatform("FAST"),
              //   component: () => import("./pages/Fast/SecondaryAllocation"),
              // },
              // {
              //   path: "/lobselection",
              //   name: "lobSelection",
              //   meta: {
              //     parentPath: "/secondaryallocation",
              //   },
              //   condition: () => userService.hasPlatform("FAST"),
              //   component: () => import("./pages/Fast/SecondaryAllocation/LobSelection.vue"),
              // },
              // {
              //   path: "/approvaldetail",
              //   name: "approvalDetail",
              //   meta: {
              //     parentPath: "/secondaryallocation",
              //   },
              //   condition: () => userService.hasPlatform("FAST"),
              //   component: () => import("./pages/Fast/SecondaryAllocation/ApprovalDetail.vue"),
              // },
              {
                path: "/fast-tracking",
                name: "FastTracking",
                meta: {
                  name: "Tracking",
                  display: true,
                  permissionName: "FAST",
                },
                condition: () => userService.hasPlatform("FAST Mono"),
                component: () => import("./pages/Fast/Tracking/index"),
              },
              {
                path: "/tracking-mpn-details",
                name: "TrackingMpnDetails",
                meta: {
                  parentPath: "/fast-tracking",
                  permissionName: "FAST",
                },
                condition: () => userService.hasPlatform("FAST Mono"),
                component: () =>
                  import("./pages/Fast/Tracking/TrackingMpnDetails.vue"),
              },
              ...DashboardRouter,
              ...DataSourceRoute,
            ],
          },
          DIAGNOSIS_ROUTER_MODULE,
          ...MDF_ROUTER_MODULE,
          {
            path: "/mystore",
            name: "Mystore",
            component: RouterView,
            meta: {
              name: "MyStore",
              display: true,
              permissionName: "MyStore",
            },
            children: [
              {
                path: "/mystore_mirror",
                name: "Mirror",
                meta: {
                  name: "Mirror",
                  display: true,
                  permissionName: "MyStore",
                },
                condition: () => userService.hasPlatform("MyStore Mirror"),
                component: () =>
                  import("./pages/operation/myStoreMirror/index.vue"),
              },
              {
                path: "/mystore_feedback/list",
                name: "Feedback",
                meta: {
                  name: "Feedback",
                  display: true,
                  permissionName: "MyStore",
                },
                condition: () => userService.hasPlatform("MyStore Feedback"),
                children: [
                  {
                    path: "/mystore_feedback/list",
                    name: "MystoreFeedbackList",
                    meta: {
                      name: "MystoreFeedbackList",
                      parentPath: "/mystore_feedback/list",
                      permissionName: "MyStore",
                    },
                    condition: () =>
                      userService.hasPlatform("MyStore Feedback"),
                    component: () =>
                      import(
                        "./pages/operation/myStoreFeedback/list/index.vue"
                      ),
                  },
                  {
                    path: "/mystore_feedback/detail",
                    name: "MystoreFeedbackDetail",
                    meta: {
                      name: "MystoreFeedbackDetail",
                      parentPath: "/mystore_feedback/list",
                      permissionName: "MyStore",
                    },
                    condition: () =>
                      userService.hasPlatform("MyStore Feedback"),
                    component: () =>
                      import(
                        "./pages/operation/myStoreFeedback/detail/index.vue"
                      ),
                  },
                ],
                component: () =>
                  import("./pages/operation/myStoreFeedback/index.vue"),
              },
              {
                path: "/mystore-npi",
                name: "mystore-npi",
                meta: {
                  name: "NPI Setup",
                  display: true,
                  permissionName: "MyStore",
                },
                condition: () => userService.hasPlatform("MyStore NPI"),
                component: () => import("./pages/operation/npi/npiIndex.vue"),
              },
              {
                path: "/mystore-uploadBanner",
                name: "mystore_uploadBanner",
                meta: {
                  name: "Mystore uploadBanner",
                  display: false,
                  parentPath: "/mystore-npi",
                  permissionName: "MyStore",
                },
                condition: () => userService.hasPlatform("MyStore NPI"),
                component: () =>
                  import(
                    "./pages/operation/npi/uploadBanner/collapseIndex.vue"
                  ),
              },
              {
                path: "/mystore-notification",
                name: "notification",
                meta: {
                  name: "Notification",
                  display: true,
                  permissionName: "MyStore",
                },
                condition: () =>
                  userService.hasPlatform("MyStore Notification"),
                component: () =>
                  import("./pages/operation/myStoreNotification/index.vue"),
              },
              {
                path: "/mystore-tracking",
                name: "MyStoreTracking",
                meta: {
                  name: "Tracking",
                  key: "mystore",
                  display: true,
                  permissionName: "MyStore",
                },
                condition: () => {
                  return (
                    userService.hasPlatform("MyStore Tracking") &&
                    (userService.hasRoleName(
                      "MyStore Tracking Mono",
                      "Permission"
                    ) ||
                      userService.hasPlatform("MyStore Tracking Carrier") ||
                      userService.hasPlatform("MyStore Tracking Multi"))
                  );
                },

                component: () =>
                  import("./pages/Implementation/Tracking/MyStoreTracking.vue"),
              },
              {
                path: "/mystore-tracking-mono",
                name: "MyStoreTrackingMono",
                meta: {
                  name: "Tracking (Mono)",
                  key: "mystore",
                  display: true,
                  permissionName: "MyStore",
                },
                condition: () => {
                  return (
                    userService.hasPlatform("MyStore Tracking") &&
                    (userService.hasRoleName(
                      "MyStore Tracking Mono",
                      "Business"
                    ) ||
                      userService.hasRoleName(
                        "MyStore Tracking Mono",
                        "Product"
                      ))
                  );
                },
                component: () =>
                  import(
                    "./pages/Implementation/Tracking/MyStoreTrackingMono.vue"
                  ),
              },
            ],
          },
          {
            path: "/myBusiness",
            name: "myBusiness",
            component: RouterView,
            meta: {
              name: "MyBusiness",
              display: true,
              permissionName: "MyBusiness",
            },
            children: [
              {
                path: "/mybusiness_mirror",
                name: "Mirror",
                meta: {
                  name: "Mirror",
                  display: true,
                  permissionName: "MyBusiness",
                },
                condition: () => userService.hasPlatform("MyBusiness Mirror"),
                component: () =>
                  import("./pages/operation/myBusinessMirror/index.vue"),
              },
              {
                path: "/mybusiness_npi",
                name: "mybusiness_npi",
                meta: {
                  name: "NPI Setup",
                  display: true,
                  onlyShowParent: true,
                  permissionName: "MyBusiness",
                },
                condition: () => userService.hasPlatform("MyBusiness NPI"),
                component: () =>
                  import("./pages/operation/mybusinessNpi/mybusinessNpi.vue"),
              },
              {
                path: "/mybusiness_addnpi",
                name: "mybusiness_addnpi",
                meta: {
                  name: "Mybuiness AddNpi",
                  display: false,
                  parentPath: "/mybusiness_npi",
                  permissionName: "MyBusiness",
                },
                condition: () => userService.hasPlatform("MyBusiness NPI"),
                component: () =>
                  import("./pages/operation/mybusinessNpi/addNpi/addNpi.vue"),
              },
              {
                path: "/mybusiness-notification",
                name: "notification",
                meta: {
                  name: "Notification",
                  display: true,
                  permissionName: "MyBusiness",
                },
                condition: () =>
                  userService.hasPlatform("MyBusiness Notification"),
                component: () =>
                  import("./pages/operation/myBusinessNotification/index.vue"),
              },
              {
                path: "/mybusiness-tracking",
                name: "MyBusinessTracking",
                meta: {
                  name: "Tracking",
                  key: "mybusiness",
                  display: true,
                  permissionName: "MyBusiness",
                },
                condition: () => userService.hasPlatform("MyBusiness Tracking"),
                component: () =>
                  import(
                    "./pages/Implementation/Tracking/MyBusinessTracking.vue"
                  ),
              },
            ],
          },
          {
            path: "/opex",
            name: "opex",
            component: () => import("./pages/Opex/indexView.vue"),
            meta: {
              name: "OPEX",
              display: true,
            },
            children: [
              {
                path: "/po_management",
                name: "po_management",
                meta: {
                  name: "PO Management",
                  permissionName: "OPEX",
                  display: true,
                },
                condition: () =>
                  userService.hasPlatform("OPEX") &&
                  (userService.hasRoleName("OPEX", "OPEX Admin") ||
                    userService.hasRoleName("OPEX", "RTM User")),
                component: () =>
                  import("./pages/Opex/POManagement/List/indexView.vue"),
              },
              {
                path: "/po_management_detail",
                name: "po_management_detail",
                meta: {
                  name: "PO Management Detail",
                  display: false,
                  permissionName: "OPEX",
                  parentPath: "/po_management",
                },
                condition: () =>
                  userService.hasPlatform("OPEX") &&
                  (userService.hasRoleName("OPEX", "OPEX Admin") ||
                    userService.hasRoleName("OPEX", "RTM User")),
                component: () =>
                  import("./pages/Opex/POManagement/Details/indexView.vue"),
              },
              {
                path: "/usage_management",
                name: "usage_management",
                meta: {
                  name: "Usage Management",
                  permissionName: "OPEX",
                  display: true,
                },
                condition: () =>
                  userService.hasPlatform("OPEX") &&
                  userService.hasRoleName("OPEX", "OPEX Admin"),
                component: () =>
                  import("./pages/Opex/UsageManagement/indexView.vue"),
              },
              {
                path: "/program_management",
                name: "program_management",
                meta: {
                  name: "Program Management",
                  permissionName: "OPEX",
                  display: true,
                },
                condition: () =>
                  userService.hasPlatform("OPEX") &&
                  userService.hasRoleName("OPEX", "OPEX Admin"),
                component: () =>
                  import("./pages/Opex/CostCenterManagement/indexView.vue"),
              },
              {
                path: "/department_role",
                name: "Department Role",
                meta: {
                  name: "Department Role",
                  permissionName: "OPEX",
                  display: true,
                },
                condition: () =>
                  userService.hasPlatform("OPEX") &&
                  userService.hasRoleName("OPEX", "OPEX Admin"),
                component: () =>
                  import("./pages/Opex/DepartmentRole/indexView.vue"),
              },
            ],
          },
          {
            path: "/index-data",
            name: "DataX",
            meta: {
              name: "DataX",
              permissionName: "FAST",
              onlyShowParent: true,
              display: true,
            },
            condition: () => {
              return userService.hasPlatform("DataX");
            },
            component: () => import("./pages/DataX/index.vue"),
          },
        ],
      },
      {
        path: "/index-Admin",
        name: "Admin",
        meta: {
          name: "Admin",
          display: true,
          permissionName: "Admin",
        },
        component: RouterView,
        children: [
          {
            path: "/center",
            name: "center",
            meta: {
              name: "User Center",
              onlyShowParent: true,
              display: true,
              permissionName: "Admin",
            },
            condition: () =>
              userService.hasPlatform("Management") ||
              userService.hasPlatform("Collaboration"),
            redirect: "/center/list",
            component: () => import("./pages/AdminBeta/Center"),
            children: [
              {
                path: "/center/list",
                name: "list",
                meta: {
                  name: "list",
                  parentPath: "/center",
                  display: false,
                  permissionName: "Admin",
                },
                component: () =>
                  import("./pages/AdminBeta/Center/components/Center.vue"),
              },
              {
                path: "/center/details",
                name: "details",
                meta: {
                  name: "details",
                  parentPath: "/center",
                  display: false,
                  permissionName: "Admin",
                },
                component: () =>
                  import("./pages/AdminBeta/Center/components/Details.vue"),
              },
              {
                path: "/center/permission",
                name: "permission",
                meta: {
                  name: "permission",
                  parentPath: "/center",
                  display: false,
                  permissionName: "Admin",
                },
                component: () =>
                  import("./pages/AdminBeta/Center/components/Permission.vue"),
              },
            ],
          },
          {
            path: "/user2",
            name: "user2",
            meta: {
              name: "User Permission",
              onlyShowParent: true,
              display: true,
              permissionName: "Admin",
            },
            condition: () =>
              userService.hasPlatform("Management") ||
              userService.hasPlatform("Collaboration"),
            component: () => import("./pages/AdminBeta/user2.vue"),
          },
          {
            path: "/history",
            name: "history",
            meta: {
              name: "Operation History",
              onlyShowParent: true,
              display: true,
              permissionName: "Admin",
            },
            condition: () =>
              userService.hasPlatform("Management") ||
              userService.hasPlatform("Collaboration"),
            component: () => import("./pages/AdminBeta/History/index.vue"),
          },
        ],
      },
    ],
  },
  {
    path: "/opex-guidance",
    name: "OPEX Guidance",
    condition: () => true,
    component: () => import("./pages/Opex/POManagement/Guidance.vue"),
  },
  {
    path: "/nav",
    name: "index",
    condition: () => true,
    component: () => import("./pages/navView.vue"),
  },
  {
    path: "/nopermission",
    name: "NoPermission",
    meta: {
      name: "NoPermition",
      parentPath: "/",
    },
    condition: () => true,
    component: () => import("./pages/other/NoPermission.vue"),
  },
  {
    path: "/protocolpopup",
    name: "ProtocolPopup",
    meta: {
      name: "ProtocolPopup",
      parentPath: "/",
    },
    condition: () => true,
    component: () => import("./pages/other/ProtocolPopup.vue"),
  },
  {
    path: "*",
    name: "index",
    meta: {
      name: "index",
    },
    condition: () => true,
    component: () => import("./pages/indexView.vue"),
  },
];
