<template>
  <div class="filters">
    <template v-if="isOutOfStock">
      <CapsuleTab
        v-model="activeView"
        :options="OutOfStockTabs"
        size="small"
        :equal="false"
        bisector
      />
      <CustomSelect
        class="width190"
        v-model="activeTime"
        :options="TimeOptions"
        size="small"
        @change="handleTimeChange"
      >
        <template #prefix>Time: </template>
      </CustomSelect>
      <CustomDatePicker
        v-if="isDaily"
        class="width190 date"
        v-model="activeDate"
        :picker-options="pickerOptions"
        :clearable="false"
      />
      <CustomSelect
        v-else
        class="width190"
        v-model="activeDate"
        :options="options"
        size="small"
      >
        <template #prefix>{{ prefix }}</template>
      </CustomSelect>
    </template>
    <CustomSelect
      :class="[isOutOfStock ? 'width190' : 'width280']"
      v-model="activeLob"
      :options="tabsList"
      size="small"
      @change="handleLobChange"
    >
      <template #prefix>LOB: </template>
    </CustomSelect>
    <div class="btns" v-if="isOutOfStock">
      <OperBtn
        class="download"
        @click="handleCheckpoints"
        v-log.click="{
          event_name: 'smart_tower_today_Inventory__out-of-stock_checkpoints',
        }"
      >
        <svg-icon data_iconName="diagnosis-inventory-check_points" />
        Check Points
      </OperBtn>
      <OperBtn
        class="download"
        @click="download"
        :disabled="downloading"
        v-load.inline="downloading"
        v-log.click="{
          event_name: 'smart_tower_today_Inventory__out-of-stock_download',
        }"
      >
        <img
          src="@/assets/imgs/diagnosis/detail/download.png"
          v-show="!downloading"
        />
        Download
      </OperBtn>
    </div>
  </div>
</template>

<script>
import CustomSelect from "@/components/CustomSelect";
import CapsuleTab from "@/components/CapsuleTab";
import CustomDatePicker from "@/pages/Event/components/CustomDatePicker.vue";
import OperBtn from "@/pages/Diagnosis/components/OperBtn.vue";
import { TimeOptions, OutOfStockTabs, Daily, Weekly } from "../constant";
import dayjs from "dayjs";

export default {
  components: {
    CustomSelect,
    CapsuleTab,
    CustomDatePicker,
    OperBtn,
  },
  props: {
    lob: {
      type: String,
      default: "",
    },
    time: {
      type: String,
      default: "",
    },
    date: {
      type: String,
      default: "",
    },
    view: {
      type: String,
      default: "",
    },
    tabsList: {
      type: Array,
      default: () => [],
    },
    isOutOfStock: {
      type: Boolean,
      default: false,
    },
    dateOptions: {
      type: Object,
      default: () => {},
    },
    downloading: {
      type: Boolean,
      default: false,
    },
    isAdmin: {
      type: Boolean,
      default: true,
    },
    isOnlyOnline: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      TimeOptions,
      OutOfStockTabs: OutOfStockTabs.filter((item) => {
        if (item.isAdmin === "All") {
          return true;
        }
        return item.isAdmin === this.isAdmin && !this.isOnlyOnline;
      }),

      pickerOptions: {
        disabledDate: (date) => {
          const maxDate = dayjs().subtract(2, "day");
          const current = dayjs(date);
          return current.isAfter(maxDate);
        },
      },
    };
  },
  computed: {
    activeLob: {
      get() {
        return this.lob;
      },
      set(val) {
        this.$emit("update:lob", val);
      },
    },
    activeTime: {
      get() {
        return this.time;
      },
      set(val) {
        this.$emit("update:time", val);
      },
    },
    activeDate: {
      get() {
        return this.date;
      },
      set(val) {
        this.$emit("update:date", val);
      },
    },
    activeView: {
      get() {
        return this.view;
      },
      set(val) {
        this.$emit("update:view", val);
      },
    },
    isDaily() {
      return this.activeTime === Daily;
    },
    options() {
      return this.dateOptions?.[this.activeTime] ?? [];
    },
    prefix() {
      return this.activeTime === Weekly ? "Weekly: " : "Quarterly: ";
    },
  },
  methods: {
    handleTimeChange(val) {
      this.activeDate =
        val === Daily
          ? dayjs().subtract(2, "day").format("YYYY-MM-DD")
          : (this.dateOptions?.[val] ?? [])?.[0]?.value ?? "";
    },

    download() {
      this.$emit("download");
    },

    handleLobChange() {
      this.$emit("lobChange");
    },

    handleCheckpoints() {
      this.$emit("checkpoints");
    },
  },
};
</script>

<style lang="scss" scoped>
.filters {
  @include flex(flex-start);
  gap: 16px;
  padding: 20px 0;
  border-bottom: 1px solid #e5e5ea;

  .width190 {
    width: 190px;
  }
  .width280 {
    width: 280px;
  }
  .btns {
    @include flex(flex-end);
    margin-left: auto;
  }
  .download {
    height: 40px;
    margin-left: auto;
    img {
      height: 16px;
      width: auto;
    }
    .svg-icon {
      font-size: 16px;
    }

    ::v-deep .expert-directive__loading {
      font-size: 12px;
      order: -1;
    }
  }
  .date {
    ::v-deep .normal {
      --fontSize: 13px;
      --borderRadius: 8px;
      --padLeft: 12px;
      --left: -12px;
      --panel-borderRadius: 8px;
      height: 40px;

      &:not(.is-disabled):hover {
        border-color: #aeaeb2;
      }
    }
  }
}
</style>
