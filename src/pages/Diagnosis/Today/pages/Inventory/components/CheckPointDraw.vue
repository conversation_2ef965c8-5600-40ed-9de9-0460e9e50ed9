<template>
  <div class="check-draw-container">
    <div class="draw-filter">
      <CapsuleTab
        v-model="view"
        :options="DrawViewOptions"
        size="small"
        :equal="false"
        bisector
      />
      <CustomSelect v-model="week" :options="weekOptions" size="small">
        <template #prefix>􀉉</template>
      </CustomSelect>
      <CustomSelect v-model="rtm" :options="rtmOptions" size="small">
        <template #prefix>RTM: </template>
      </CustomSelect>
      <CustomSelect v-model="lob" :options="lobOptions" size="small">
        <template #prefix>LOB: </template>
      </CustomSelect>
    </div>

    <div class="table-container">
      <el-table :data="data" :span-method="spanMethod" :max-height="'100%'">
        <el-table-column
          v-for="column in columns"
          :key="column?.prop"
          v-bind="column"
          :width="column?.width * zoomValue"
        />
      </el-table>
    </div>
  </div>
</template>

<script>
import CapsuleTab from "@/components/CapsuleTab";
import CustomSelect from "@/components/CustomSelect";
import { DrawViewOptions } from "../constant";
import { formatCheckPointData } from "../format";
import { CheckData } from "../mock";

export default {
  components: {
    CapsuleTab,
    CustomSelect,
  },
  data() {
    return {
      view: DrawViewOptions?.[0]?.value ?? "",
      DrawViewOptions,
      week: "",
      weekOptions: [],
      rtm: "",
      rtmOptions: [],
      lob: "",
      lobOptions: [],

      data: [],
      columns: [],
      spanArr: [],
    };
  },
  computed: {
    zoomValue() {
      return this.$store.state.newNavZoomValue;
    },
  },
  mounted() {
    this.getData();
  },
  methods: {
    getData() {
      const { code = 0, data = {} } = CheckData;
      if (code === 0) {
        const newResult = formatCheckPointData(data, this.rtm);
        this.data = newResult?.data ?? [];
        this.columns = newResult?.columns ?? [];
        this.spanArr = newResult?.spanArr ?? [];
      }
    },

    spanMethod({ rowIndex, columnIndex }) {
      if (columnIndex === 0) {
        const rowspan = this.spanArr?.[rowIndex] ?? 1;
        const colspan = rowspan > 0 ? 1 : 0;
        return {
          rowspan,
          colspan,
        };
      }
    },
  },
};
</script>

<style lang="scss" scoped>
.check-draw-container {
  display: flex;
  flex-direction: column;
  flex: 1;
  min-height: 0;
  overflow: hidden;

  .draw-filter {
    flex: 0 0 auto;
    display: grid;
    gap: 16px;
    grid-template-columns: auto 1fr 1fr 1fr;
    margin: 20px 0;
  }

  .table-container {
    flex: 1;
    min-height: 0;
    overflow: hidden;
  }

  ::v-deep .el-table {
    // 表格使用max-height自适应

    .el-table__cell {
      border-right: 1px solid #e5e5e5;
    }

    .el-table__header {
      .el-table__cell:last-child {
        border-right: none;
      }
    }

    .el-table__body {
      .el-table__row {
        .el-table__cell:last-child {
          border-right: none;
        }
        &:last-child {
          .el-table__cell {
            border-bottom: none;
          }
        }
      }
    }

    // 确保表格体能正确滚动
    // .el-table__body-wrapper {
    //   overflow-y: auto;
    // }

    // // 固定表头
    // .el-table__header-wrapper {
    //   overflow: hidden;
    // }
  }
}
</style>
