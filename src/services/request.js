import axios from "axios";
import axiosRetry from "axios-retry";
import qs from "qs";
import { Message } from "element-ui";
import customMessage from "../components/SmartIsland";
axios.defaults.headers["Content-Type"] = "application/json;charset=utf-8";
import * as Sentry from "@sentry/vue";

export const reportLog = (res) => {
  if (res.status !== 200) return;
  const { config, data } = res;
  const { baseURL, url, params } = config;
  const { code = 0 } = data;
  if (code !== 0) {
    Sentry.captureMessage("Code Error", {
      contexts: {
        res: {
          code,
          baseURL,
          url,
          params: JSON.stringify(params),
          message: res?.data?.msg,
        },
      },
      level: Sentry.Severity.Info,
    });
  }
};
const basicConfig = {
  // 超时
  timeout: 10000,
};

const TIMEOUT_ERROR_LIST = [
  "/group-plan/required-number",
  "/experiment/materials/upload/",
  "/dashboard/report/",
  "dashboard/mono-advanced-experiment",
];

const SHOW_TOAST_LIST = [
  "dashboard/carrier-experiment",
  "dashboard/multi-experiment",
  "dashboard/mono-experiment",
  "dashboard/experiment",
];
const NOT_SHOW_MESSAGE = [
  "experiment/mono-online/data/upload",
  "experiment/multi-online/data/upload",
  "experiment/carrier-online/data/upload",
  "experiment/online/init",
  "/experiment/tags/report",
];
const matchURL = (url, list) => {
  return !!list.filter((i) => url.indexOf(i) > -1).length;
};

const basicRequestInterceptors = {
  onFulfilled: (config) => {
    //config.headers.token = token
    // * 针对定制实验 post 请求转换特定的参数类型
    if (
      config.url.includes("/custom-experiment/") &&
      config.method === "post"
    ) {
      const bodyData = config.data;
      for (const key in bodyData) {
        if (
          (key === "y_type_custom" ||
            key === "grouping_variables_custom" ||
            key === "convariates_custom") &&
          bodyData[key] instanceof Array
        ) {
          bodyData[key] = JSON.stringify(bodyData[key]);
          // console.log("transform data form custom experiment.", bodyData);
        }
      }
    }
    return config;
  },
  onRejected: (error) => {
    Promise.reject(error);
  },
};
const WHOLE = [
  "dashboard/multi-experiment",
  "dashboard/carrier-experiment",
  "dashboard/mono-advanced-experiment",
  "dashboard/experiment",
  "dashboard/mono-online-experiment",
  "dashboard/mono-experiment/",
];
const SPECIA_MESSAGE = ["/experiment/tags/report"];
const basicResponseInterceptors = {
  onFulfilled: (res) => {
    const data = (res && res.data) || { code: 10086, msg: "error" };
    // * ingoreInterceptor 用于标记由调用测处理返回结构体
    if (res.config.ignoreInterceptor) {
      return res.data;
    }
    if (data.code === 0) {
      if (!matchURL(res.config.url, WHOLE)) {
        return data.data;
      } else {
        return data;
      }
    } else {
      reportLog(res);
      const { config } = res;
      if (matchURL(config.url, SHOW_TOAST_LIST) && typeof data !== "string") {
        // Message({
        //   showClose: true,
        //   message: res.message,
        //   type: "error",
        // });
        customMessage({
          type: "error",
          message: res.message,
        });
      }

      return data;
    }
  },

  onRejected: (error) => {
    const { response = {}, config = {}, request = {}, message } = error;
    let isTimeout = message.indexOf("timeout") > -1;
    if (
      matchURL(config.url, TIMEOUT_ERROR_LIST) &&
      (request.status === 504 || isTimeout)
    ) {
      // Message({
      //   showClose: true,
      //   message: "Request timeout, please try again later. ",
      //   type: "error",
      // });
      customMessage({
        type: "error",
        message: "Request timeout, please try again later. ",
      });
    } else if (!matchURL(config.url, NOT_SHOW_MESSAGE)) {
      // Message({
      //   showClose: true,
      //   message: JSON.stringify(response.data),
      //   type: "error",
      // });
      customMessage({
        type: "error",
        message: response.data,
      });
    } else if (matchURL(config.url, SPECIA_MESSAGE)) {
      customMessage({
        type: "error",
        message: response.data.message,
      });
    }
    return Promise.reject(response);
  },
};

const expertRequestInterceptors = {
  onFulfilled: (config) => {
    return config;
  },
  onRejected: (error) => {
    Promise.reject(error);
  },
};
const expertResponseInterceptors = {
  onFulfilled: (res) => {
    reportLog(res);
    return res;
  },
  onRejected: (error) => {
    customMessage({
      type: "error",
      message: error,
    });
    return Promise.reject(error);
  },
};

const aiLabRequestInterceptors = {
  onFulfilled: (config) => {
    return config;
  },
  onRejected: (error) => {
    Promise.reject(error);
  },
};
const aiLabResponseInterceptors = {
  onFulfilled: (res) => {
    if (
      res.data.code === 0 ||
      res.data.code === 10011 ||
      res.data.code === 10001 ||
      res.data.code === 10004
    ) {
      return res.data;
    } else {
      reportLog(res);
    }

    //   const data = (res && res.data) || { code: 10086, msg: 'error' }
    // if (data.code === 0) {
    //   return data.data
    // } else {
    //   return data;
    // }
  },
  onRejected: (error) => {
    return Promise.reject(error);
  },
};
const aiLabDownResponseInterceptors = {
  onFulfilled: (res) => {
    reportLog(res);
    return res;
  },
  onRejected: (error) => {
    return Promise.reject(error);
  },
};

const taggingDownResponseInterceptors = {
  onFulfilled: (res) => {
    reportLog(res);
    return res;
  },
  onRejected: (error) => {
    customMessage({
      type: "error",
      message: error,
    });
    return Promise.reject(error);
  },
};
const fastRequestInterceptors = {
  onFulfilled: (config) => {
    return config;
  },
  onRejected: (error) => {
    return Promise.reject(error);
  },
};
const fastDownResponseInterceptors = {
  onFulfilled: (res) => {
    if (res?.status === 200) {
      if (res?.data?.code !== 0 && res?.data?.msg) {
        Message({
          showClose: true,
          message: res?.data?.msg,
          type: "error",
        });
        reportLog(res);
      }
    }
    return res;
  },
  onRejected: (error) => {
    customMessage({
      type: "error",
      message: error,
    });
    return Promise.reject(error);
  },
};
const secondAlloRequestInterceptors = {
  onFulfilled: (config) => {
    return config;
  },
  onRejected: (error) => {
    return Promise.reject(error);
  },
};
const secondAlloResponseInterceptors = {
  onFulfilled: (res) => {
    if (res.status === 200) {
      if (res?.data?.code !== 0) {
        Message({
          showClose: true,
          message: res?.data?.msg,
          type: "error",
        });
        reportLog(res);
      }
      return res?.data;
    }
  },
  onRejected: (error) => {
    customMessage({
      type: "error",
      message: error,
    });
    return Promise.reject(error);
  },
};
const secondAlloDownResponseInterceptors = {
  onFulfilled: (res) => {
    return res;
  },
  onRejected: (error) => {
    customMessage({
      type: "error",
      message: error,
    });
    return Promise.reject(error);
  },
};
const videoRequestInterceptors = {
  onFulfilled: (config) => {
    if (config.method === "get") {
      config.paramsSerializer = function (params) {
        return qs.stringify(params, { arrayFormat: "repeat" });
      };
    }
    return config;
  },
  onRejected: (error) => {
    Promise.reject(error);
  },
};
const videoResponseInterceptors = {
  onFulfilled: (res) => {
    if (res.status === 200) {
      if (res?.data?.code !== 0 && res?.data?.msg) {
        reportLog(res);
        Message({
          showClose: true,
          message: res?.data?.msg,
          type: "error",
        });
      }
      return res.data;
    }
    return res?.data;
  },
  onRejected: () => {
    return {
      code: 400,
      mag: "Network Error",
    };
  },
};
const myStoreRquestInterceptors = {
  onFulfilled: (config) => {
    return config;
  },
  onRejected: (error) => {
    Promise.reject(error);
  },
};
const myStoreResponseInterceptors = {
  onFulfilled: (res) => {
    if (res.data.code === 0 || res.data.code === 10011) {
      return res.data;
    } else {
      reportLog(res);
      return res;
    }
  },
  onRejected: (error) => {
    customMessage({
      type: "error",
      message: error,
    });
    return Promise.reject(error);
  },
};
const myBusinessRquestInterceptors = {
  onFulfilled: (config) => {
    return config;
  },
  onRejected: (error) => {
    Promise.reject(error);
  },
};
const myBusinessResponseInterceptors = {
  onFulfilled: (res) => {
    reportLog(res);
    return res;
  },
  onRejected: (error) => {
    customMessage({
      type: "error",
      message: error,
    });
    return Promise.reject(error);
  },
};

const cityRequestInterceptors = {
  onFulfilled: (config) => {
    if (config.method === "get") {
      config.paramsSerializer = function (params) {
        return qs.stringify(params, { arrayFormat: "repeat" });
      };
    }
    return config;
  },
  onRejected: (error) => {
    Promise.reject(error);
  },
};

const cityResponseInterceptors = {
  onFulfilled: (res) => {
    const { signal } = res?.config?.params || {};
    let aborted = signal && signal.aborted;
    if (res && res.status === 200) {
      if (res?.data?.code !== 0 && res?.data?.msg) {
        reportLog(res);
        Message({
          showClose: true,
          message: res?.data?.msg,
          type: "error",
        });
      }
      return {
        ...res.data,
        aborted,
        params: res?.config?.params,
      };
    }
    return {
      ...res.data,
      aborted,
      params: res?.config?.params,
    };
  },
  onRejected: (error) => {
    console.error("error: ", error);
    return {
      code: 400,
      msg: "Network Error",
    };
  },
};

const operationRequestInterceptors = {
  onFulfilled: (config) => {
    if (config.method === "get") {
      config.paramsSerializer = function (params) {
        return qs.stringify(params, { arrayFormat: "repeat" });
      };
    }
    return config;
  },
  onRejected: (error) => {
    Promise.reject(error);
  },
};
const operationResponseInterceptors = {
  onFulfilled: (res) => {
    if (res?.status === 200) {
      if (res?.data?.code !== 0 && res?.data?.msg) {
        reportLog(res);
        Message({
          showClose: true,
          message: res?.data?.msg,
          type: "error",
        });
      }
    }
    return res;
  },
  onRejected: () => {
    return {
      code: 400,
      mag: "Network Error",
    };
  },
};
const forecastRequestInterceptors = {
  onFulfilled: (config) => {
    return config;
  },
  onRejected: (error) => {
    Promise.reject(error);
  },
};
const forecastResponseInterceptors = {
  onFulfilled: (res) => {
    if (!res.fileName && res.status === 200) {
      if (res?.data?.code !== 0 && res?.data?.msg) {
        reportLog(res);
        Message({
          showClose: true,
          message: res?.data?.msg,
          type: "error",
        });
      }
      return res.data;
    }
    return res;
  },
  onRejected: (error) => {
    customMessage({
      type: "error",
      message: error,
    });
    return Promise.reject(error);
  },
};
const trackingResponseInterceptors = {
  onFulfilled: (res) => {
    if (res.status === 200) {
      if (res?.data?.code !== 0 && res?.data?.msg) {
        reportLog(res);
        Message({
          showClose: true,
          message: res?.data?.msg,
          type: "error",
        });
      }
      if (
        [
          "/mystore/notlogin/download",
          "/mybusiness/notlogin/download",
        ].includes(res.config.url)
      ) {
        return res;
      }
      return res.data;
    }
    return res?.data;
  },
  onRejected: (error) => {
    if (error.message !== "Operation canceled by the user.") {
      Message({
        showClose: true,
        message: error.config?.url + "  " + error,
        type: "error",
      });
    }
    if (error.toString().includes("timeout")) {
      return { code: 10086, msg: "error" };
    }
    return Promise.reject(error);
  },
};
const trackingRquestInterceptors = {
  onFulfilled: (config) => {
    if (config.method === "get") {
      config.paramsSerializer = function (params) {
        return qs.stringify(params, { arrayFormat: "repeat" });
      };
    }
    return config;
  },
  onRejected: (error) => {
    Promise.reject(error);
  },
};
const fastLiteRequestInterceptors = {
  onFulfilled: (config) => {
    return config;
  },
  onRejected: (error) => {
    Promise.reject(error);
  },
};
const fastLiteResponseInterceptors = {
  onFulfilled: (res) => {
    if (res.status === 200) {
      if (res?.data?.code !== 0 && res?.data?.msg) {
        reportLog(res);
        Message({
          showClose: true,
          message: res?.data?.msg,
          type: "error",
        });
      }
      return res.data;
    }
    return res;
  },
  onRejected: (error) => {
    customMessage({
      type: "error",
      message: error,
    });
    return Promise.reject(error);
  },
};

const towerRequestInterceptors = {
  onFulfilled: (config) => {
    return config;
  },
  onRejected: (error) => {
    Promise.reject(error);
  },
};
const towerResponseInterceptors = {
  onFulfilled: (res) => {
    reportLog(res);
    return res;
  },
  onRejected: (error) => {
    return Promise.reject(error);
  },
};

const newBaseResponseInterceptors = {
  onFulfilled: (res) => {
    if (res.status === 200) {
      if (res?.data?.code !== 0 && res?.data?.msg) {
        if (res?.config?.url !== "/program_desc/edit") {
          Message({
            showClose: true,
            message: res?.data?.msg,
            type: "error",
          });
        }
        reportLog(res);
      }
    }
    return res?.request?.responseType === "blob" ? res : res?.data;
  },
  onRejected: (error) => {
    Promise.reject(error);
    console.log(error);
    if (!error?.config?.disableErrorMsg) {
      Message({
        showClose: true,
        message: error?.config?.url ?? "" + "  " + error,
        type: "error",
      });
    }
    if (error.toString().includes("timeout"))
      return { code: 10086, msg: "error" };
  },
};

const newBaseRequestInterceptors = {
  onFulfilled: (config) => {
    if (config.method === "get") {
      config.paramsSerializer = function (params) {
        return qs.stringify(params, { arrayFormat: "repeat" });
      };
    }
    return config;
  },
  onRejected: (error) => {
    Promise.reject(error);
  },
};

const fastLiteCpfRequestInterceptors = {
  onFulfilled: (config) => {
    return config;
  },
  onRejected: (error) => {
    Promise.reject(error);
  },
};
const fastLiteCpfResponseInterceptors = {
  onFulfilled: (res) => {
    if (res.status === 200) {
      if (
        res?.data?.code !== 0 &&
        res?.data?.msg &&
        !res?.config.disableErrorMsg
      ) {
        Message({
          showClose: true,
          message: res?.data?.msg,
          type: "error",
        });
        reportLog(res);
      }
      return res.data;
    }
    return res;
  },
  onRejected: (error) => {
    if (error.message === "canceled") {
      return Promise.reject(error);
    }
    customMessage({
      type: "error",
      message: error,
    });
    return Promise.reject(error);
  },
};

const eventRequestInterceptors = {
  onFulfilled: (config) => {
    if (config.method === "get") {
      config.paramsSerializer = function (params) {
        return qs.stringify(params, { arrayFormat: "repeat" });
      };
    }
    return config;
  },
  onRejected: (error) => {
    Promise.reject(error);
  },
};

const eventResponseInterceptors = {
  onFulfilled: (res) => {
    if (res.status === 200) {
      if (
        res?.data?.code !== 0 &&
        res?.data?.msg &&
        !res?.config.disableErrorMsg
      ) {
        Message({
          showClose: true,
          message:
            res?.data?.msg instanceof Array
              ? res?.data?.msg[0]?.msg ?? ""
              : res?.data?.msg,
          type: "error",
        });
      }
    }
    return res?.request?.responseType === "blob" ? res : res?.data;
  },
  onRejected: (error) => {
    Promise.reject(error);
    if (error.toString().includes("timeout"))
      return {
        code: 10086,
        msg: "Time out, please reduce the number of uploaded items and try again.",
      };
  },
};

const powerResponseInterceptors = {
  onFulfilled: (res) => {
    if (res.status === 200) {
      const noMessageMap = [
        "/sim2/info",
        "/simulationv2",
        "/ai/simulation",
        "/file/upload",
      ];
      const { url } = res.config;
      if (
        res?.data?.code !== 0 &&
        res?.data?.msg &&
        !res?.data?.msg.includes("timeout")
      ) {
        if (!matchURL(url, noMessageMap)) {
          const message = (() => {
            if ([10030, 10031].includes(res?.data?.code)) {
              return "Unable to display due to system error. Please try again.";
            }
            return res?.data?.msg;
          })();
          customMessage({
            type: "error",
            message,
          });
        }
        reportLog(res);
      }
    }
    return res?.request?.responseType === "blob" ? res : res?.data;
  },
  onRejected: (error) => {
    // TODO: xxx
    Promise.reject(error);
    if (error.toString().includes("timeout"))
      return { code: 10086, msg: "error" };
  },
};

const perfReviewResponseInterceptors = {
  onFulfilled: (res) => {
    if (res.status === 200) {
      if (res?.data?.code !== 0 && res?.data?.msg) {
        reportLog(res);
        customMessage({
          type: "error",
          message: res?.data?.msg,
        });
      }
    }
    return res?.request?.responseType === "blob" ? res : res?.data;
  },
  onRejected: (error) => {
    customMessage({
      type: "error",
      message: error,
    });
    return Promise.reject(error);
  },
};

const datasourceMultiRequestInterceptors = {
  onFulfilled: (config) => {
    return config;
  },
  onRejected: (error) => {
    Promise.reject(error);
  },
};
const datasourceMultiResponseInterceptors = {
  onFulfilled: (res) => {
    if (res.status === 200) {
      if (res?.data?.code !== 0 && res?.data?.msg) {
        if (![10030, 10040].includes(res?.data?.code)) {
          Message({
            showClose: true,
            message: res?.data?.msg,
            type: "error",
          });
        }
        reportLog(res);
      }
      return res.data;
    }
    return res;
  },
  onRejected: (error) => {
    customMessage({
      type: "error",
      message: error,
    });
    return Promise.reject(error);
  },
};
const datasourceMultiDownResponseInterceptors = {
  onFulfilled: (res) => {
    reportLog(res);
    return res;
  },
  onRejected: (error) => {
    return Promise.reject(error);
  },
};

const adminRequestInterceptors = {
  onFulfilled: (config) => {
    if (config.method === "get") {
      config.paramsSerializer = function (params) {
        return qs.stringify(params, { arrayFormat: "repeat" });
      };
    }
    return config;
  },
  onRejected: (error) => {
    Promise.reject(error);
  },
};
const adminResponseInterceptors = {
  onFulfilled: (res) => {
    if (
      res.config.url === "/manage/user/list/download" ||
      res.config.url === "/manage/user/batch/mould/download"
    ) {
      return res;
    } else if (
      res.data.code === 0 ||
      res.data.code === 10011 ||
      res.data.code === 10001 ||
      res.data.code === 10004
    ) {
      return res.data;
    } else {
      reportLog(res);
    }
  },
  onRejected: (error) => {
    return Promise.reject(error);
  },
};
const mdfSetUpRequestInterceptors = {
  onFulfilled: (config) => {
    return config;
  },
  onRejected: (error) => {
    Promise.reject(error);
  },
};
const mdfRequestInterceptors = {
  onFulfilled: (config) => {
    return config;
  },
  onRejected: (error) => {
    Promise.reject(error);
  },
};
const mdfSetUpResponseInterceptors = {
  onFulfilled: (res) => {
    reportLog(res);
    return res.data;
  },
  onRejected: (error) => {
    customMessage({
      type: "error",
      message: error,
    });
    return Promise.reject(error);
  },
};
const mdfResponseInterceptors = {
  onFulfilled: (res) => {
    if (res.status === 200) {
      if (
        res?.data?.code !== 0 &&
        res?.data?.message &&
        !res?.config.disableErrorMsg
      ) {
        customMessage({
          message:
            res?.data?.message instanceof Array
              ? res?.data?.message[0]?.message ?? ""
              : res?.data?.message,
          type: "error",
        });
        reportLog(res);
      }
    }
    return res?.request?.responseType === "blob" ? res : res?.data;
  },
  onRejected: (error) => {
    if (error.toString().includes("timeout")) {
      Message({
        showClose: true,
        message: "Request timeout, please try again later.",
        type: "error",
      });
    }
    return Promise.reject(error);
  },
};

const mdfRTMResponseInterceptors = {
  onFulfilled: (res) => {
    if (res.status === 200) {
      if (
        res.config.url !== "/data_portal/planner/upload_data" &&
        res?.data?.code !== 0 &&
        res?.data?.message
      ) {
        // Message({
        //   showClose: true,
        //   message:
        //     res?.data?.message instanceof Array
        //       ? res?.data?.message[0]?.message ?? ""
        //       : res?.data?.message,
        //   type: "error",
        // });
      }
      reportLog(res);
    }
    return res?.request?.responseType === "blob" ? res : res?.data;
  },
  onRejected: (error) => {
    if (error.toString().includes("timeout")) {
      Message({
        showClose: true,
        message: "Request timeout, please try again later.",
        type: "error",
      });
    }
    return Promise.reject(error);
  },
};

const forecastPlatformRequestInterceptors = {
  onFulfilled: (config) => {
    if (config.method === "get") {
      config.paramsSerializer = function (params) {
        return qs.stringify(params, { arrayFormat: "repeat" });
      };
    }
    return config;
  },
  onRejected: (error) => {
    Promise.reject(error);
  },
};
const forecastPlatformResponseInterceptors = {
  onFulfilled: (res) => {
    if (res.status === 200) {
      if (
        res?.data?.code !== 0 &&
        res?.data?.msg &&
        res.config.url !== "/scan/verify"
      ) {
        customMessage({
          showClose: true,
          message:
            res?.data?.msg instanceof Array
              ? res?.data?.msg[0]?.msg ?? ""
              : res?.data?.msg,
          type: "error",
        });
      }
      reportLog(res);
    }
    return res?.request?.responseType === "blob" ? res : res?.data;
  },
  onRejected: (error) => {
    if (error.toString().includes("timeout")) {
      Message({
        showClose: true,
        message: "Request timeout, please try again later.",
        type: "error",
      });
    }
    return Promise.reject(error);
  },
};

const diagnosisRequestInterceptors = {
  onFulfilled: (config) => {
    if (config.method === "get") {
      config.paramsSerializer = function (params) {
        return qs.stringify(params, { arrayFormat: "repeat" });
      };
    }
    return config;
  },
  onRejected: (error) => {
    Promise.reject(error);
  },
};

const diagnosisResponseInterceptors = {
  onFulfilled: (res) => {
    if (res.status === 200) {
      if (
        res?.data?.code !== 0 &&
        res?.data?.msg &&
        !res?.config.disableErrorMsg
      ) {
        customMessage({
          duration: 5000,
          message:
            res?.data?.msg instanceof Array
              ? res?.data?.msg[0]?.msg ?? ""
              : res?.data?.msg,
          type: "error",
        });
      }
    }
    return res?.request?.responseType === "blob" ? res : res?.data;
  },
  onRejected: (error) => {
    Promise.reject(error);
  },
};

const diagnosisInventoryRequestInterceptors = {
  onFulfilled: (config) => {
    if (config.method === "get") {
      config.paramsSerializer = function (params) {
        return qs.stringify(params, { arrayFormat: "repeat" });
      };
    }
    return config;
  },
  onRejected: (error) => {
    Promise.reject(error);
  },
};

const diagnosisInventoryResponseInterceptors = {
  onFulfilled: (res) => {
    if (res.status === 200) {
      if (
        res?.data?.code !== 0 &&
        res?.data?.msg &&
        !res?.config.disableErrorMsg
      ) {
        customMessage({
          duration: 5000,
          message:
            res?.data?.msg instanceof Array
              ? res?.data?.msg[0]?.msg ?? ""
              : res?.data?.msg,
          type: "error",
        });
      }
    }
    return res?.request?.responseType === "blob" ? res : res?.data;
  },
  onRejected: (error) => {
    Promise.reject(error);
  },
};

const NLPTrackingRequestInterceptors = {
  onFulfilled: (config) => {
    return config;
  },
  onRejected: (error) => {
    Promise.reject(error);
  },
};

const NLPTrackingResponseInterceptors = {
  onFulfilled: (res) => {
    const data = (res && res.data) || { code: 10086, msg: "error" };
    // * ingoreInterceptor 用于标记由调用测处理返回结构体
    if (res.config.ignoreInterceptor) {
      return res.data;
    }
    if (data.code === 0) {
      return data.data;
    } else {
      reportLog(res);
      return data;
    }
  },

  onRejected: (error) => {
    const { response = {}, config = {}, request = {}, message } = error;
    let isTimeout = message.indexOf("timeout") > -1;
    if (
      matchURL(config.url, TIMEOUT_ERROR_LIST) &&
      (request.status === 504 || isTimeout)
    ) {
      Message({
        showClose: true,
        message: "Request timeout, please try again later. ",
        type: "error",
      });
    } else if (!matchURL(config.url, NOT_SHOW_MESSAGE)) {
      Message({
        showClose: true,
        message: JSON.stringify(response.data),
        type: "error",
      });
    }
    return Promise.reject(response);
  },
};

const forecastDemandRequestInterceptors = {
  onFulfilled: (config) => {
    if (config.method === "get") {
      config.paramsSerializer = function (params) {
        return qs.stringify(params, { arrayFormat: "repeat" });
      };
    }
    return config;
  },
  onRejected: (error) => {
    Promise.reject(error);
  },
};
const forecastDemandResponseInterceptors = {
  onFulfilled: (res) => {
    if (res.status === 200) {
      if (res?.data?.code !== 0 && res?.data?.msg) {
        if (![10030, 10040, 10031].includes(res?.data?.code)) {
          //文件上传校验 特殊状态码10030
          customMessage({
            showClose: true,
            message:
              res?.data?.msg instanceof Array
                ? res?.data?.msg[0]?.msg ?? ""
                : res?.data?.msg,
            type: "error",
          });
        }
      }
      reportLog(res);
    }
    return res?.request?.responseType === "blob" ? res : res?.data;
  },
  onRejected: (error) => {
    if (error.toString().includes("timeout")) {
      Message({
        showClose: true,
        message: "Request timeout, please try again later.",
        type: "error",
      });
    }
    return Promise.reject(error);
  },
};

const overviewBoardResponseInterceptors = {
  onFulfilled: (res) => {
    if (res.status === 200) {
      if (res?.data?.code !== 0 && res?.data?.msg) {
        Message({
          showClose: true,
          message: res?.data?.msg,
          type: "error",
        });
        reportLog(res);
      }
    }
    return res?.request?.responseType === "blob" ? res : res?.data;
  },
  onRejected: (error) => {
    if (!axios.isCancel(error)) {
      Promise.reject(error);
      Message({
        showClose: true,
        message: error,
        type: "error",
      });
      if (error.toString().includes("timeout"))
        return { code: 10086, msg: "error" };
    }

    Promise.reject(error);
    return { code: 10086, msg: "error" };
  },
};

const overviewBoardRequestInterceptors = {
  onFulfilled: (config) => {
    if (config.method === "get") {
      config.paramsSerializer = function (params) {
        return qs.stringify(params, { arrayFormat: "repeat" });
      };
    }
    return config;
  },
  onRejected: (error) => {
    Promise.reject(error);
  },
};

const commonBackendRequestInterceptors = {
  onFulfilled: (config) => {
    if (config.method === "get") {
      config.paramsSerializer = function (params) {
        return qs.stringify(params, { arrayFormat: "repeat" });
      };
    }
    return config;
  },
  onRejected: (error) => {
    Promise.reject(error);
  },
};
const commonBackendResponseInterceptors = {
  onFulfilled: (res) => {
    if (res.status === 200) {
      if (
        res?.data?.code !== 0 &&
        res?.data?.msg &&
        !res?.config.disableErrorMsg
      ) {
        customMessage({
          duration: 5000,
          message:
            res?.data?.msg instanceof Array
              ? res?.data?.msg[0]?.msg ?? ""
              : res?.data?.msg,
          type: "error",
        });
      }
      reportLog(res);
    }
    return res?.request?.responseType === "blob" ? res : res?.data;
  },
  onRejected: (error) => {
    if (error.toString().includes("timeout")) {
      customMessage({
        message: "Request timeout, please try again later.",
        type: "error",
      });
    }
    return Promise.reject(error);
  },
};

const llmPlaygroundRequestInterceptors = {
  onFulfilled: (config) => {
    if (config.method === "get") {
      config.paramsSerializer = function (params) {
        return qs.stringify(params, { arrayFormat: "repeat" });
      };
    }
    return config;
  },
  onRejected: (error) => {
    Promise.reject(error);
  },
};
const llmPlaygroundResponseInterceptors = {
  onFulfilled: (res) => {
    if (res.status === 200) {
      // if (
      //   res?.data?.code !== 0 &&
      //   res?.data?.msg &&
      //   !res?.config.disableErrorMsg
      // ) {
      //   customMessage({
      //     duration: 5000,
      //     message:
      //       res?.data?.msg instanceof Array
      //         ? res?.data?.msg[0]?.msg ?? ""
      //         : res?.data?.msg,
      //     type: "error",
      //   });
      // }
      reportLog(res);
    }
    return res?.request?.responseType === "blob" ? res : res?.data;
  },
  onRejected: (error) => {
    // if (error.toString().includes("timeout")) {
    //   customMessage({
    //     message: "Request timeout, please try again later.",
    //     type: "error",
    //   });
    // }
    return Promise.reject(error);
  },
};

const commonRequestInterceptors = {
  onFulfilled: (config) => {
    if (config.method === "get") {
      config.paramsSerializer = function (params) {
        return qs.stringify(params, { arrayFormat: "repeat" });
      };
    }
    return config;
  },
  onRejected: (error) => {
    Promise.reject(error);
  },
};
const commonResponseInterceptors = {
  onFulfilled: (res) => {
    if (res.status === 200) {
      if (
        res?.data?.code !== 0 &&
        res?.data?.msg &&
        !res?.config.disableErrorMsg
      ) {
        customMessage({
          duration: 5000,
          message:
            res?.data?.msg instanceof Array
              ? res?.data?.msg[0]?.msg ?? ""
              : res?.data?.msg,
          type: "error",
        });
      }
      reportLog(res);
    }
    return res?.request?.responseType === "blob" ? res : res?.data;
  },
  onRejected: (error) => {
    if (error.toString().includes("timeout")) {
      customMessage({
        message: "Request timeout, please try again later.",
        type: "error",
      });
    }
    return Promise.reject(error);
  },
};

let optionsAbTest = {
  // axios中请求配置有baseURL选项，表示请求URL公共部分
  baseURL: "/abtest",
  ...basicConfig,
};
let optionsExpert = {
  // axios中请求配置有baseURL选项，表示请求URL公共部分
  baseURL: "/expert",
  ...basicConfig,
};
let optionsAiLab = {
  // axios中请求配置有baseURL选项，表示请求URL公共部分
  baseURL: "/ailab/",
  timeout: 20000,
  ...basicConfig,
};
let optionsAIlab = {
  baseURL: "/ailab/",
  timeout: 20000,
  ...basicConfig,
};
let optionsTagging = {
  baseURL: "/tagging/",
  ...basicConfig,
};

let optionsVideo = {
  baseURL: "/expert/video",
  ...basicConfig,
};
let optionsFastApi = {
  baseURL: "/allocation",
  "content-type": "application/json;",
  "Access-Control-Allow-Origin": "*",
  ...basicConfig,
};
let optionsFast = {
  baseURL: "/allocation",
  ...basicConfig,
};
let optionsFastUploadApi = {
  baseURL: "/allocation",
  timeout: 300000,
};

let optionsSecondAllo = {
  baseURL: "/allocation",
  ...basicConfig,
  timeout: 60000,
};

let optionsFeedback = {
  baseURL: "/mystore",
  ...basicConfig,
  timeout: 30000, // 改为30s超时
};

let optionsMyBusiness = {
  baseURL: "/mybusiness",
  ...basicConfig,
};
let optionsPos = {
  baseURL: "/allocation",
  ...basicConfig,
};

let optionsCityInsights = {
  baseURL: "/expert",
  timeout: 50000,
};
let optionsOperation = {
  // axios中请求配置有baseURL选项，表示请求URL公共部分
  baseURL: "/expert",
  ...basicConfig,
};

let optionsFastCpf = {
  baseURL: "/fastlite",
  timeout: 180000,
};

let forecast = {
  baseURL: "/allocation",
  ...basicConfig,
};
const tracking = {
  // axios中请求配置有baseURL选项，表示请求URL公共部分
  baseURL: "/common/tracking",
  ...basicConfig,
  timeout: 60000,
};

const forecastTracking = {
  // axios中请求配置有baseURL选项，表示请求URL公共部分
  baseURL: "/forecast_platform/tracking/forecast",
  ...basicConfig,
  // * 单独为 forecast-tracking 页面接口配置 timeout 时间
  timeout: 20000,
};
const optionsTower = {
  baseURL: "/expert/tower/lite",
  ...basicConfig,
};

const optionsTowerV1 = {
  baseURL: "/expert/tower/v1",
  ...basicConfig,
};

const optionsTowerRealTime = {
  baseURL: "/tower/realtime",
  ...basicConfig,
  timeout: 20000,
};

const optionsEvent = {
  baseURL: "/expert/stamp",
  ...basicConfig,
  timeout: 30 * 1000,
};
const optionsAdmin = {
  baseURL: "/user_center/",
  ...basicConfig,
};
const optionsPowerBox = {
  baseURL: "/ailab/powerbox",
  ...basicConfig,
  timeout: 120 * 1000,
};
const optionsPerfReview = {
  baseURL: "/performaflow/perfreview",
  ...basicConfig,
  timeout: 20 * 1000,
};

let datasourceMulti = {
  baseURL: "/fastlite",
  timeout: 1000 * 60 * 3,
};
let mdfSetUp = {
  baseURL: "/rebate/setup",
  timeout: 50000,
};

let optionsMdf = {
  baseURL: "/rebate",
  timeout: 60000,
};

let mdfRTMData = {
  baseURL: "/rebate",
  timeout: 50000,
};

let opex = {
  // axios中请求配置有baseURL选项，表示请求URL公共部分
  baseURL: "/performaflow/opex",
  ...basicConfig,
};

let overviewBoard = {
  baseURL: "/overview_board",
  ...basicConfig,
  timeout: 50 * 1000,
};

let optionsFaucet = {
  // axios中请求配置有baseURL选项，表示请求URL公共部分
  baseURL: "/expert/faucet",
  ...basicConfig,
};

const optionsDiagnosis = {
  baseURL: "/tower/smart_tower",
  ...basicConfig,
  timeout: 30 * 1000,
};

const optionsDiagnosisInventory = {
  baseURL: "/smart_tower_inventory",
  ...basicConfig,
  timeout: 30 * 1000,
};
const dataX = {
  baseURL: "/datax/datax",
  ...basicConfig,
};

const faucetRequset = axios.create(optionsFaucet);
const ForecastPlatform = {
  baseURL: "/forecast_platform",
  timeout: 50000,
};
const optionsCommonbackend = {
  baseURL: "/commonbackend",
  ...basicConfig,
  timeout: 30 * 1000,
};
const taggingRequest = axios.create(optionsTagging);
const abTestRequest = axios.create({
  ...optionsAbTest,
  timeout: 120 * 1000,
});
const abTestDownRequest = axios.create({
  ...optionsAbTest,
  timeout: 120 * 1000,
});

const abTestDownBoldRequest = axios.create(optionsAbTest);
const expertRequest = axios.create(optionsExpert);
const ailabRequest = axios.create(optionsAiLab);
const ailabDownlRequest = axios.create(optionsAIlab);
const fastApiRequest = axios.create(optionsFastApi);
const fastRequest = axios.create(optionsFast);
const fastUploadApiRequest = axios.create(optionsFastUploadApi);
const videoRequest = axios.create(optionsVideo);
const secondAlloRequest = axios.create(optionsSecondAllo);
const secondAlloDownRequest = axios.create(optionsSecondAllo);
const feedbackRequest = axios.create(optionsFeedback);
const cityRequest = axios.create(optionsCityInsights);
const operationRequest = axios.create(optionsOperation);
const trackingRequest = axios.create(tracking);
const forecastTrackingRequest = axios.create(forecastTracking);
const commonbackendRequest = axios.create(optionsCommonbackend);
// only retry forecast-tracking page request
axiosRetry(forecastTrackingRequest, {
  retries: 1,
  shouldResetTimeout: true,
  retryCondition: (e) => {
    if (e.code === "ECONNABORTED") {
      return true;
    }
  },
});
const fdServiceRequest = axios.create(forecast);
const towerRequest = axios.create(optionsTower);
const fastLiteRequest = axios.create(optionsPos);
const fastLiteCpfRequest = axios.create(optionsFastCpf);
const mybusinessRequest = axios.create(optionsMyBusiness);
const towerV1Request = axios.create(optionsTowerV1);
const towerRealTime = axios.create(optionsTowerRealTime);
const eventRequest = axios.create(optionsEvent);
const adminRequest = axios.create(optionsAdmin);
const powerBoxRequest = axios.create(optionsPowerBox);
const perfReviewRequest = axios.create(optionsPerfReview);
const datasourceMultiRequest = axios.create(datasourceMulti);
const datasourceMultiDownRequest = axios.create(datasourceMulti);
const mdfSetUpRequest = axios.create(mdfSetUp);
const mdfRequest = axios.create(optionsMdf);
const mdfRTMRequest = axios.create(mdfRTMData);
const opexRequest = axios.create(opex);
const forecastPlatformRequest = axios.create(ForecastPlatform);
const diagnosisRequest = axios.create(optionsDiagnosis);
const diagnosisInventoryRequest = axios.create(optionsDiagnosisInventory);
const NLPTrackingRequest = axios.create(optionsAbTest);
const forecastDemandRequest = axios.create(datasourceMulti);
const overviewBoardRequest = axios.create(overviewBoard);
const channelComplianceRequest = axios.create({
  baseURL: "/overview_board/channel_compliance",
  ...basicConfig,
  timeout: 120 * 1000,
});
const appealRequest = axios.create({
  baseURL: "/forecast_platform/suspension",
  ...basicConfig,
  timeout: 30 * 1000,
});
const aiprogramRequest = axios.create({
  baseURL: "/forecast_platform/ai_program",
  ...basicConfig,
  timeout: 30 * 1000,
});
const llmPlaygroundRequest = axios.create({
  baseURL: "/aiplatform/llm",
  ...basicConfig,
  timeout: 30 * 1000,
});
const commonBackendRequest = axios.create({
  baseURL: "/commonbackend",
  ...basicConfig,
  timeout: 30 * 1000,
});
const dataXRequest = axios.create(dataX);
dataXRequest.interceptors.request.use(
  commonRequestInterceptors.onFulfilled,
  commonRequestInterceptors.onRejected
);

dataXRequest.interceptors.response.use(
  forecastDemandResponseInterceptors.onFulfilled,
  forecastDemandResponseInterceptors.onRejected
);
abTestRequest.interceptors.request.use(
  basicRequestInterceptors.onFulfilled,
  basicRequestInterceptors.onRejected
);
abTestRequest.interceptors.response.use(
  basicResponseInterceptors.onFulfilled,
  basicResponseInterceptors.onRejected
);

abTestDownRequest.interceptors.request.use(
  basicRequestInterceptors.onFulfilled,
  basicRequestInterceptors.onRejected
);
abTestDownRequest.interceptors.response.use(
  basicResponseInterceptors.onFulfilled,
  basicResponseInterceptors.onRejected
);
abTestDownBoldRequest.interceptors.request.use(
  expertRequestInterceptors.onFulfilled,
  expertRequestInterceptors.onRejected
);
abTestDownBoldRequest.interceptors.response.use(
  expertRequestInterceptors.onFulfilled,
  expertRequestInterceptors.onRejected
);
expertRequest.interceptors.request.use(
  expertRequestInterceptors.onFulfilled,
  expertRequestInterceptors.onRejected
);
expertRequest.interceptors.response.use(
  expertResponseInterceptors.onFulfilled,
  expertResponseInterceptors.onRejected
);

ailabRequest.interceptors.request.use(
  aiLabRequestInterceptors.onFulfilled,
  aiLabRequestInterceptors.onRejected
);
ailabRequest.interceptors.response.use(
  aiLabResponseInterceptors.onFulfilled,
  aiLabResponseInterceptors.onRejected
);

ailabDownlRequest.interceptors.request.use(
  aiLabRequestInterceptors.onFulfilled,
  aiLabRequestInterceptors.onRejected
);
ailabDownlRequest.interceptors.response.use(
  aiLabDownResponseInterceptors.onFulfilled,
  aiLabDownResponseInterceptors.onRejected
);

fastApiRequest.interceptors.request.use(
  fastRequestInterceptors.onFulfilled,
  fastRequestInterceptors.onRejected
);
fastApiRequest.interceptors.response.use(
  fastDownResponseInterceptors.onFulfilled,
  fastDownResponseInterceptors.onRejected
);

taggingRequest.interceptors.request.use(
  taggingDownResponseInterceptors.onFulfilled,
  taggingDownResponseInterceptors.onRejected
);
taggingRequest.interceptors.response.use(
  taggingDownResponseInterceptors.onFulfilled,
  taggingDownResponseInterceptors.onRejected
);

videoRequest.interceptors.request.use(
  videoRequestInterceptors.onFulfilled,
  videoRequestInterceptors.onRejected
);
videoRequest.interceptors.response.use(
  videoResponseInterceptors.onFulfilled,
  videoResponseInterceptors.onRejected
);

fastRequest.interceptors.request.use(
  fastRequestInterceptors.onFulfilled,
  fastRequestInterceptors.onRejected
);
fastRequest.interceptors.response.use(
  fastDownResponseInterceptors.onFulfilled,
  fastDownResponseInterceptors.onRejected
);

fastUploadApiRequest.interceptors.request.use(
  fastRequestInterceptors.onFulfilled,
  fastRequestInterceptors.onRejected
);
fastUploadApiRequest.interceptors.response.use(
  fastDownResponseInterceptors.onFulfilled,
  fastDownResponseInterceptors.onRejected
);

secondAlloRequest.interceptors.request.use(
  secondAlloRequestInterceptors.onFulfilled,
  secondAlloRequestInterceptors.onRejected
);
secondAlloRequest.interceptors.response.use(
  secondAlloResponseInterceptors.onFulfilled,
  secondAlloResponseInterceptors.onRejected
);

secondAlloDownRequest.interceptors.request.use(
  secondAlloDownResponseInterceptors.onFulfilled,
  secondAlloDownResponseInterceptors.onRejected
);
secondAlloDownRequest.interceptors.response.use(
  secondAlloDownResponseInterceptors.onFulfilled,
  secondAlloDownResponseInterceptors.onRejected
);

feedbackRequest.interceptors.request.use(
  myStoreRquestInterceptors.onFulfilled,
  myStoreRquestInterceptors.onRejected
);
feedbackRequest.interceptors.response.use(
  myStoreResponseInterceptors.onFulfilled,
  myStoreResponseInterceptors.onRejected
);

mybusinessRequest.interceptors.request.use(
  myBusinessRquestInterceptors.onFulfilled,
  myBusinessRquestInterceptors.onRejected
);
mybusinessRequest.interceptors.request.use(
  myBusinessResponseInterceptors.onFulfilled,
  myBusinessResponseInterceptors.onRejected
);

cityRequest.interceptors.request.use(
  cityRequestInterceptors.onFulfilled,
  cityRequestInterceptors.onRejected
);
cityRequest.interceptors.response.use(
  cityResponseInterceptors.onFulfilled,
  cityResponseInterceptors.onRejected
);

operationRequest.interceptors.request.use(
  operationRequestInterceptors.onFulfilled,
  operationRequestInterceptors.onRejected
);
operationRequest.interceptors.response.use(
  operationResponseInterceptors.onFulfilled,
  operationResponseInterceptors.onRejected
);
fdServiceRequest.interceptors.request.use(
  forecastRequestInterceptors.onFulfilled,
  forecastRequestInterceptors.onRejected
);
fdServiceRequest.interceptors.response.use(
  forecastResponseInterceptors.onFulfilled,
  forecastResponseInterceptors.onRejected
);
trackingRequest.interceptors.request.use(
  trackingRquestInterceptors.onFulfilled,
  trackingRquestInterceptors.onRejected
);
trackingRequest.interceptors.response.use(
  trackingResponseInterceptors.onFulfilled,
  trackingResponseInterceptors.onRejected
);

forecastTrackingRequest.interceptors.request.use(
  trackingRquestInterceptors.onFulfilled,
  trackingRquestInterceptors.onRejected
);
forecastTrackingRequest.interceptors.response.use(
  trackingResponseInterceptors.onFulfilled,
  trackingResponseInterceptors.onRejected
);
towerRequest.interceptors.request.use(
  towerRequestInterceptors.onFulfilled,
  towerRequestInterceptors.onRejected
);
towerRequest.interceptors.response.use(
  towerResponseInterceptors.onFulfilled,
  towerResponseInterceptors.onRejected
);
fastLiteRequest.interceptors.request.use(
  fastLiteRequestInterceptors.onFulfilled,
  fastLiteRequestInterceptors.onRejected
);
fastLiteRequest.interceptors.response.use(
  fastLiteResponseInterceptors.onFulfilled,
  fastLiteResponseInterceptors.onRejected
);

towerV1Request.interceptors.request.use(
  newBaseRequestInterceptors.onFulfilled,
  newBaseRequestInterceptors.onRejected
);

towerV1Request.interceptors.response.use(
  newBaseResponseInterceptors.onFulfilled,
  newBaseResponseInterceptors.onRejected
);
towerRealTime.interceptors.request.use(
  newBaseRequestInterceptors.onFulfilled,
  newBaseRequestInterceptors.onRejected
);
towerRealTime.interceptors.response.use(
  newBaseResponseInterceptors.onFulfilled,
  newBaseResponseInterceptors.onRejected
);

fastLiteCpfRequest.interceptors.request.use(
  fastLiteCpfRequestInterceptors.onFulfilled,
  fastLiteCpfRequestInterceptors.onRejected
);
fastLiteCpfRequest.interceptors.response.use(
  fastLiteCpfResponseInterceptors.onFulfilled,
  fastLiteCpfResponseInterceptors.onRejected
);

eventRequest.interceptors.request.use(
  eventRequestInterceptors.onFulfilled,
  eventRequestInterceptors.onRejected
);

eventRequest.interceptors.response.use(
  eventResponseInterceptors.onFulfilled,
  eventResponseInterceptors.onRejected
);
powerBoxRequest.interceptors.request.use(
  newBaseRequestInterceptors.onFulfilled,
  newBaseRequestInterceptors.onRejected
);
powerBoxRequest.interceptors.response.use(
  powerResponseInterceptors.onFulfilled,
  powerResponseInterceptors.onRejected
);
perfReviewRequest.interceptors.request.use(
  newBaseRequestInterceptors.onFulfilled,
  newBaseRequestInterceptors.onRejected
);
perfReviewRequest.interceptors.response.use(
  perfReviewResponseInterceptors.onFulfilled,
  perfReviewResponseInterceptors.onRejected
);

datasourceMultiRequest.interceptors.request.use(
  datasourceMultiRequestInterceptors.onFulfilled,
  datasourceMultiRequestInterceptors.onRejected
);
datasourceMultiRequest.interceptors.response.use(
  datasourceMultiResponseInterceptors.onFulfilled,
  datasourceMultiResponseInterceptors.onRejected
);

datasourceMultiDownRequest.interceptors.request.use(
  datasourceMultiRequestInterceptors.onFulfilled,
  datasourceMultiRequestInterceptors.onRejected
);
datasourceMultiRequest.interceptors.response.use(
  datasourceMultiDownResponseInterceptors.onFulfilled,
  datasourceMultiDownResponseInterceptors.onRejected
);

adminRequest.interceptors.request.use(
  adminRequestInterceptors.onFulfilled,
  adminRequestInterceptors.onRejected
);

adminRequest.interceptors.response.use(
  adminResponseInterceptors.onFulfilled,
  adminResponseInterceptors.onRejected
);
mdfSetUpRequest.interceptors.request.use(
  mdfSetUpRequestInterceptors.onFulfilled,
  mdfSetUpRequestInterceptors.onRejected
);

mdfSetUpRequest.interceptors.response.use(
  mdfSetUpResponseInterceptors.onFulfilled,
  mdfSetUpResponseInterceptors.onRejected
);
mdfRequest.interceptors.request.use(
  mdfRequestInterceptors.onFulfilled,
  mdfRequestInterceptors.onRejected
);

mdfRequest.interceptors.response.use(
  mdfResponseInterceptors.onFulfilled,
  mdfResponseInterceptors.onRejected
);

mdfRTMRequest.interceptors.request.use(
  mdfRequestInterceptors.onFulfilled,
  mdfRequestInterceptors.onRejected
);

mdfRTMRequest.interceptors.response.use(
  mdfRTMResponseInterceptors.onFulfilled,
  mdfRTMResponseInterceptors.onRejected
);
faucetRequset.interceptors.response.use(
  mdfRTMResponseInterceptors.onFulfilled,
  mdfRTMResponseInterceptors.onRejected
);

opexRequest.interceptors.response.use(
  newBaseResponseInterceptors.onFulfilled,
  newBaseResponseInterceptors.onRejected
);
opexRequest.interceptors.request.use(
  newBaseRequestInterceptors.onFulfilled,
  newBaseRequestInterceptors.onRejected
);

forecastPlatformRequest.interceptors.request.use(
  forecastPlatformRequestInterceptors.onFulfilled,
  forecastPlatformRequestInterceptors.onRejected
);

forecastPlatformRequest.interceptors.response.use(
  forecastPlatformResponseInterceptors.onFulfilled,
  forecastPlatformResponseInterceptors.onRejected
);

diagnosisRequest.interceptors.request.use(
  diagnosisRequestInterceptors.onFulfilled,
  diagnosisRequestInterceptors.onRejected
);

diagnosisRequest.interceptors.response.use(
  diagnosisResponseInterceptors.onFulfilled,
  diagnosisResponseInterceptors.onRejected
);

diagnosisInventoryRequest.interceptors.request.use(
  diagnosisInventoryRequestInterceptors.onFulfilled,
  diagnosisInventoryRequestInterceptors.onRejected
);

diagnosisInventoryRequest.interceptors.response.use(
  diagnosisInventoryResponseInterceptors.onFulfilled,
  diagnosisInventoryResponseInterceptors.onRejected
);

NLPTrackingRequest.interceptors.request.use(
  NLPTrackingRequestInterceptors.onFulfilled,
  NLPTrackingRequestInterceptors.onRejected
);
NLPTrackingRequest.interceptors.response.use(
  NLPTrackingResponseInterceptors.onFulfilled,
  NLPTrackingResponseInterceptors.onRejected
);

forecastDemandRequest.interceptors.request.use(
  forecastDemandRequestInterceptors.onFulfilled,
  forecastDemandRequestInterceptors.onRejected
);

forecastDemandRequest.interceptors.response.use(
  forecastDemandResponseInterceptors.onFulfilled,
  forecastDemandResponseInterceptors.onRejected
);

overviewBoardRequest.interceptors.response.use(
  overviewBoardResponseInterceptors.onFulfilled,
  overviewBoardResponseInterceptors.onRejected
);
overviewBoardRequest.interceptors.request.use(
  overviewBoardRequestInterceptors.onFulfilled,
  overviewBoardRequestInterceptors.onRejected
);

channelComplianceRequest.interceptors.response.use(
  commonResponseInterceptors.onFulfilled,
  commonResponseInterceptors.onRejected
);
channelComplianceRequest.interceptors.request.use(
  commonRequestInterceptors.onFulfilled,
  commonRequestInterceptors.onRejected
);

appealRequest.interceptors.response.use(
  commonResponseInterceptors.onFulfilled,
  commonResponseInterceptors.onRejected
);
appealRequest.interceptors.request.use(
  commonRequestInterceptors.onFulfilled,
  commonRequestInterceptors.onRejected
);

aiprogramRequest.interceptors.response.use(
  commonResponseInterceptors.onFulfilled,
  commonResponseInterceptors.onRejected
);
aiprogramRequest.interceptors.request.use(
  commonRequestInterceptors.onFulfilled,
  commonRequestInterceptors.onRejected
);

llmPlaygroundRequest.interceptors.request.use(
  llmPlaygroundRequestInterceptors.onFulfilled,
  llmPlaygroundRequestInterceptors.onRejected
);
llmPlaygroundRequest.interceptors.response.use(
  llmPlaygroundResponseInterceptors.onFulfilled,
  llmPlaygroundResponseInterceptors.onRejected
);

commonBackendRequest.interceptors.request.use(
  commonBackendRequestInterceptors.onFulfilled,
  commonBackendRequestInterceptors.onRejected
);
commonBackendRequest.interceptors.response.use(
  commonBackendResponseInterceptors.onFulfilled,
  commonBackendResponseInterceptors.onRejected
);

commonbackendRequest.interceptors.response.use(
  commonResponseInterceptors.onFulfilled,
  commonResponseInterceptors.onRejected
);
commonbackendRequest.interceptors.request.use(
  commonRequestInterceptors.onFulfilled,
  commonRequestInterceptors.onRejected
);
export {
  feedbackRequest,
  videoRequest,
  abTestRequest,
  expertRequest,
  ailabRequest,
  ailabDownlRequest,
  taggingRequest,
  fastApiRequest,
  fastRequest,
  fastUploadApiRequest,
  cityRequest,
  operationRequest,
  secondAlloRequest,
  secondAlloDownRequest,
  trackingRequest,
  fdServiceRequest,
  forecastTrackingRequest,
  abTestDownRequest,
  towerRequest,
  fastLiteRequest,
  mybusinessRequest,
  towerV1Request,
  towerRealTime,
  fastLiteCpfRequest,
  eventRequest,
  adminRequest,
  powerBoxRequest,
  perfReviewRequest,
  datasourceMultiRequest,
  datasourceMultiDownRequest,
  abTestDownBoldRequest,
  mdfRequest,
  mdfSetUpRequest,
  mdfRTMRequest,
  opexRequest,
  faucetRequset,
  forecastPlatformRequest,
  diagnosisRequest,
  diagnosisInventoryRequest,
  NLPTrackingRequest,
  forecastDemandRequest,
  overviewBoardRequest,
  channelComplianceRequest,
  appealRequest,
  aiprogramRequest,
  llmPlaygroundRequest,
  commonBackendRequest,
  commonbackendRequest,
  dataXRequest,
};
