<template>
  <div class="inventory-container">
    <Title :time="data_as_of_date" :loading="loading" :tipList="tipList" />
    <template v-if="isAdmin">
      <SecondaryTitle>Today Summary</SecondaryTitle>
      <div
        class="summary-wrapper"
        v-log.module.visible="{
          event_name: 'smart_tower_today_Inventory_summary_expose',
        }"
        v-log:5.stay="{
          event_name: 'smart_tower_today_Inventory_summary_stay',
        }"
      >
        <SummaryItem
          v-for="(item, index) in summary"
          :key="index"
          :lob="item.lob"
          :loading="loading"
          :tableData="item.details"
        />
      </div>
    </template>
    <SecondaryTitle>Performance</SecondaryTitle>
    <CustomCard class="lob-wrapper">
      <ModuleStay v-if="hasLob" :performance="activePerformance" />
      <el-tabs v-model="activePerformance">
        <el-tab-pane
          v-for="(item, index) in PerformanceOptions"
          :key="index"
          :label="item.label"
          :name="item.value"
          :disabled="isModuleLoading"
        />
      </el-tabs>
      <FilterView
        :lob.sync="activeLob"
        :time.sync="timeValue"
        :date.sync="dateValue"
        :view.sync="viewTabValue"
        :dateOptions="dateOptions"
        :tabsList="tabsList"
        :isOutOfStock="isOutOfStock"
        :downloading="downloading"
        :isAdmin="isAdmin"
        :isOnlyOnline="isOnlyOnline"
        @download="handleDownload"
        @lobChange="handleLobChange"
        @checkpoints="handleCheckpoints"
      />
      <OutOfStockView
        ref="outofstock"
        v-show="isOutOfStock"
        :lob="activeLob"
        :time="timeValue"
        :date="dateValue"
        :view="viewTabValue"
        :hasLob="hasLob"
        :isAdmin="isAdmin"
        :performance="activePerformance"
        :subLobOptions="outofstockLobOptions"
        :RtmMap="RtmMap"
        :roles="roles"
        @update-loading="updateLoading(3, $event)"
        @downloaded="downloading = false"
      />
      <WOISupply
        v-if="isAdmin"
        v-show="!isOutOfStock"
        :lob="activeLob"
        :hasLob="hasLob"
        :performance="activePerformance"
        @update-loading="updateLoading(0, $event)"
      />
      <Rolling7days
        v-if="!isOnlyEDU"
        v-show="isInStock"
        :lob="activeLob"
        :hasLob="hasLob"
        :subLobOptions="rolling7DaysSubLobOptions"
        :rtmOptions="rtmOptions"
        :isAdmin="isAdmin"
        @update-loading="updateLoading(1, $event)"
      />
      <InventoryFlow
        v-if="!isOnlyEDU"
        v-show="isInventory"
        :lob="activeLob"
        :hasLob="hasLob"
        :subLobOptions="subLobOptions"
        :rtmOptions="inventoryRtmOptions"
        :isAdmin="isAdmin"
        @update-loading="updateLoading(2, $event)"
      />
    </CustomCard>
    <FullScreenLoading v-if="fullLoading" />
    <PointStay
      v-log.module.visible="{
        event_name: 'smart_tower_today_Inventory_expose',
      }"
      v-log.stay="{
        event_name: 'smart_tower_today_Inventory_stay',
      }"
    />
    <CustomDrawer
      class="check-drawer"
      :visible.sync="drawer"
      :title="'Out-of Stock%'"
      size="medium"
    >
      <CheckPointDraw />
    </CustomDrawer>
  </div>
</template>

<script>
import diagnosisInventoryService from "@/services/DiagnosisInventoryService";
import { sendLog } from "@/utils/sendLog";
import CustomCard from "@/pages/Diagnosis/components/CustomCard.vue";
import Title from "../../components/Title.vue";
import SecondaryTitle from "./components/SecondaryTitle.vue";
import SummaryItem from "./components/SummaryItem.vue";
import WOISupply from "./components/WOISupply.vue";
import Rolling7days from "./components/Rolling7Days.vue";
import InventoryFlow from "./components/InventoryFlow.vue";
import {
  draftSummary,
  PerformanceOptions,
  OutOfStock,
  InStock,
  Inventory,
  Product,
  TimeOptions,
  Role2Rtm,
  WOI,
} from "./constant";
import {
  formatSummaryData,
  formatDateOptions,
  formatRtmOptions,
} from "./format";
import { userService } from "@/services/UserService";
import { CPF_PLATFORM_NAME, CPF_PLATFORM_ADMIN } from "../../../routes";
import FullScreenLoading from "@/pages/Diagnosis/components/FullScreenLoading.vue";
import FilterView from "./components/FilterView.vue";
import OutOfStockView from "./components/OutOfStock.vue";
import dayjs from "dayjs";
import { mapGetters } from "vuex";
import PointStay from "@/components/PointStay.vue";
import ModuleStay from "./components/ModuleStay.vue";
import CustomDrawer from "@/pages/Diagnosis/components/CustomDrawer.vue";
import CheckPointDraw from "./components/CheckPointDraw.vue";

export default {
  name: "InventoryPage",
  components: {
    Title,
    SecondaryTitle,
    CustomCard,
    SummaryItem,
    WOISupply,
    Rolling7days,
    InventoryFlow,
    FullScreenLoading,
    FilterView,
    OutOfStockView,
    PointStay,
    ModuleStay,
    CustomDrawer,
    CheckPointDraw,
  },
  data() {
    return {
      tipList: [],
      data_as_of_date: "",
      summary: draftSummary,
      loading: false,
      menu: [],
      activeLob: "",
      moduleLoadingStates: [false, false, false, false],
      fullLoading: false,

      // v1.3新增
      inventoryRtmOptions: [],

      // v1.4
      activePerformance: "",
      timeValue: "",
      dateValue: "",
      dateOptions: {},
      viewTabValue: "",
      RtmMap: [],
      downloading: false,

      // v1.5
      outofstockMenu: [],

      // v1.6
      drawer: false,
    };
  },
  provide() {
    return {
      getPerformanceMenu: (params) => this.getPerformanceMenu(params),
    };
  },
  computed: {
    ...mapGetters(["getRoleName"]),
    roles() {
      return this.getRoleName(CPF_PLATFORM_NAME).map(
        (item) => Role2Rtm?.[item] ?? ""
      );
    },
    isAdmin() {
      return userService.hasRoleName(CPF_PLATFORM_NAME, CPF_PLATFORM_ADMIN);
    },
    isOnlyOnline() {
      return this.roles.every((item) => item === "Channel Online");
    },
    isOnlyEDU() {
      return this.roles.every((item) => item === "EDU");
    },
    tabsList() {
      return this.menu.map((item) => {
        return {
          label: item?.lob ?? "",
          value: item?.lob ?? "",
        };
      });
    },
    hasLob() {
      return !!this.activeLob && this.activeLob !== "0";
    },
    rolling7DaysSubLobOptions() {
      const currentLob = this.menu.find((v) => v.lob === this.activeLob);
      if (currentLob === undefined) {
        return [];
      }
      const { sub_lobs = [] } = currentLob;
      const options = [];
      for (let i = 0; i < sub_lobs.length; i++) {
        if (sub_lobs[i] === "All") {
          continue;
        }
        options.push({
          label: sub_lobs[i],
          value: sub_lobs[i],
        });
      }
      return options;
    },
    subLobOptions() {
      const currentLob = this.menu.find((v) => v.lob === this.activeLob);
      if (currentLob === undefined) {
        return [];
      }
      return (currentLob.sub_lobs ?? []).map((v) => ({
        label: v,
        value: v,
      }));
    },
    rtmOptions() {
      const currentRTM = this.menu.find((v) => v.lob === this.activeLob);
      if (currentRTM === undefined) {
        return [];
      }
      return (currentRTM.rtms ?? []).map((v) => ({
        label: v.rtm,
        value: v.rtm,
        business_types: v.business_types,
      }));
    },
    isModuleLoading() {
      return this.moduleLoadingStates.some((state) => state);
    },
    isOutOfStock() {
      return this.activePerformance === OutOfStock;
    },
    isInStock() {
      return this.activePerformance === InStock;
    },
    isInventory() {
      return this.activePerformance === Inventory;
    },
    PerformanceOptions() {
      if (this.isAdmin) {
        return PerformanceOptions;
      } else {
        const options = PerformanceOptions.filter((item) => item.value !== WOI);
        return this.isOnlyEDU ? options.slice(0, 1) : options;
      }
    },
    outofstockLobOptions() {
      const currentLob = this.outofstockMenu.find(
        (v) => v.lob === this.activeLob
      );
      return (currentLob?.sub_lobs ?? []).map((v) => ({
        label: v,
        value: v,
      }));
    },
  },
  watch: {
    activeLob(v, oldv) {
      if (!oldv) return;
      sendLog({
        event_name: "smart_tower_today_Inventory_LOB_switch_click",
        event_type: "click",
        extra: {
          type: `${v}`,
        },
      });
    },
    PerformanceOptions: {
      handler(v) {
        this.activePerformance = v?.[0]?.value ?? "";
      },
      deep: true,
      immediate: true,
    },
    activePerformance(v, oldv) {
      if (!oldv) return;
      sendLog({
        event_name: "smart_tower_today_Inventory_outinwoiinv_click",
        event_type: "click",
        extra: {
          type: `${v}`,
        },
      });
    },
    viewTabValue(v, oldv) {
      if (!oldv) return;
      sendLog({
        event_name:
          "smart_tower_today_Inventory__out-of-stock_typeswitch_click",
        event_type: "click",
        extra: {
          type: `${v}`,
        },
      });
    },
    timeValue(v, oldv) {
      if (!oldv) return;
      sendLog({
        event_name:
          "smart_tower_today_Inventory__out-of-stock_dateswitch_click",
        event_type: "click",
        extra: {
          type: `${v}`,
        },
      });
    },
  },
  mounted() {
    this.init();
  },
  methods: {
    init() {
      this.getSummary();
      this.getMenu();
      this.getTipList();
    },
    getSummary() {
      this.loading = true;
      diagnosisInventoryService
        .getSummary()
        .then(({ code, data }) => {
          if (code !== 0) return;
          const { data_as_of_date, summary } = data;
          this.data_as_of_date = data_as_of_date;
          this.summary = formatSummaryData(summary);
        })
        .catch((err) => {
          console.log(err);
        })
        .finally(() => {
          this.loading = false;
        });
    },
    getMenu() {
      this.fullLoading = true;
      Promise.all([
        diagnosisInventoryService.getMenu(),
        diagnosisInventoryService.getInventoryMenu(),
        diagnosisInventoryService.getOutOfStockMenu(),
        diagnosisInventoryService.getOutOfStockTimeMenu(),
      ])
        .then(([commonMenu, invMenu, rtmMenu, timeMenu]) => {
          if (
            commonMenu?.code === 0 &&
            invMenu?.code === 0 &&
            rtmMenu?.code === 0 &&
            timeMenu?.code === 0
          ) {
            this.menu = commonMenu?.data ?? [];
            this.activeLob = commonMenu?.data?.[0]?.lob ?? "";
            this.inventoryRtmOptions = (invMenu?.data ?? []).map((item) => {
              return {
                label: item.rtm,
                value: item.rtm,
                business_types: item.business_types,
              };
            });

            // v1.4
            this.timeValue = TimeOptions?.[0]?.value;
            this.dateValue = dayjs().subtract(2, "day").format("YYYY-MM-DD");
            this.dateOptions = formatDateOptions(timeMenu?.data ?? {});
            this.viewTabValue = Product;
            this.RtmMap = formatRtmOptions(rtmMenu?.data ?? {});
          }
        })
        .catch((err) => {
          console.log(err);
        })
        .finally(() => {
          this.$nextTick(() => {
            this.fullLoading = false;
          });
        });
    },
    getTipList() {
      diagnosisInventoryService
        .getTipList({ page: "Smart Tower Inventory" })
        .then(({ code, data }) => {
          if (code !== 0) {
            // TODO: error handle
            return;
          }
          this.tipList = data;
        })
        .catch((err) => {
          console.log(err);
        })
        .finally(() => {});
    },
    updateLoading(index, loading) {
      this.moduleLoadingStates.splice(index, 1, loading);
    },
    handleDownload() {
      this.downloading = true;
      this.$refs.outofstock?.handleDownload();
    },

    handleLobChange() {
      this.$refs.outofstock?.handleLobChange();
    },

    async getPerformanceMenu() {
      try {
        const { code = 0, data = [] } =
          await diagnosisInventoryService.getPerformancePersonMenu({
            time_type: this.timeValue,
            date: this.dateValue,
          });
        this.outofstockMenu = code === 0 ? data : [];
      } catch (error) {
        console.warn(error);
      }
    },

    handleCheckpoints() {
      this.drawer = true;
    },
  },
};
</script>

<style lang="scss" scoped>
.inventory-container {
  padding: 0 40px 40px;
  min-height: 100vh;

  .summary-wrapper {
    @include flex(flex-start, stretch);
    gap: 16px;
    flex-wrap: wrap;
    margin-bottom: 32px;

    > div {
      flex-basis: calc(50% - 8px);
      max-width: calc(50% - 8px);
      box-sizing: border-box;
    }
  }

  .lob-wrapper {
    padding-top: 16px;
    position: relative;
  }

  ::v-deep .el-table {
    border: 1px solid #dfdee4;
    border-radius: 12px;

    &::before {
      display: none;
    }

    thead {
      .el-table__cell {
        font-size: 13px;
        font-weight: normal;
        color: #6e6e73;
        background-color: #f2f2f5;
        padding: 12px 16px;

        .cell {
          line-height: 16px;
          padding: 0;
        }
      }
    }
    .el-table__body-wrapper {
      tr:hover > td {
        background-color: inherit;
      }
    }
    .el-table__body {
      .el-table__cell {
        font-size: 14px;
        color: #1c1c1e;
        border-bottom-color: #dfdee4;
        padding: 12px 16px;

        .cell {
          line-height: 20px;
          padding: 0;
        }
      }

      tr:hover > td.el-table__cell {
        background-color: inherit;
      }
      tr.hover-row > td.el-table__cell {
        background-color: inherit;
      }

      .total-row {
        .el-table__cell {
          background-color: #fafafa;
        }

        .cell {
          font-weight: $fontMedium;
        }
      }

      .custom-table-last-cell {
        border-bottom-color: transparent;
      }
    }
  }

  ::v-deep .el-tabs {
    min-height: 56px;
    margin-bottom: 0;

    .el-tabs__nav-wrap::after {
      height: 1px;
      background-color: #e5e5ea;
    }

    .el-tabs__header {
      margin-bottom: 0;
    }

    .el-tabs__active-bar {
      height: 2px;
      background-color: #000;
      border-radius: 2px;
    }

    .el-tabs__item {
      height: 56px;
      line-height: 56px;
      font-size: 20px;
      font-weight: $fontMedium;
      color: #aeaeb2;
      padding: 0 16px;

      &:nth-child(2) {
        padding-left: 0;
      }

      &.is-active {
        color: #1c1c1e;

        .tabs-item svg {
          color: #1c1c1e;
        }
      }

      &.is-disabled {
        color: #bfbfbf;

        .tabs-item svg {
          color: #bfbfbf;
        }
      }

      .tabs-item {
        @include flex(flex-start);

        svg {
          width: 24px;
          height: 24px;
          color: #aeaeb2;
          margin-right: 2px;
        }
      }
    }

    .el-tabs__item:focus.is-active.is-focus:not(:active) {
      box-shadow: none;
      border-radius: 0;
    }

    .el-tabs__nav-prev,
    .el-tabs__nav-next {
      height: 100%;
      @include flex(center);
    }
  }

  ::v-deep {
    .panel-title {
      font-size: 16px;
      font-weight: $fontMedium;
      color: #1c1c1e;
    }

    .break-word {
      word-break: break-word;
    }

    .load-warp {
      width: 2em;
      height: 2em;
    }

    .warning {
      color: #ffae3d;
    }

    .attention {
      color: #f63f54;
    }

    .grey {
      color: #aeaeb2;
    }

    .tip-wrapper {
      @include flex(flex-start);

      svg {
        width: 20px;
        height: 20px;
        margin-right: 2px;
      }

      .prompt svg {
        width: 16px;
        height: 16px;
      }
    }
  }

  ::v-deep .check-drawer.medium .el-drawer {
    width: 1000px !important;

    .el-drawer__body {
      padding-top: 28px;
      .title {
        border-bottom: 1px solid #e5e5ea;
        margin-bottom: 0;
        padding-bottom: 12px;
      }
    }
  }
}
</style>
<style>
@import "../../../common.scss";
</style>
