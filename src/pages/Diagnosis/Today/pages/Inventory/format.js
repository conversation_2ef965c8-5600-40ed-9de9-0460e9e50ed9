import { thousandths, _toFixed } from "@/utils/formatNumber";
import {
  isNullOrUndefinedOrEmptyString,
  formatToPercentageInt,
} from "../../../formatData";
import {
  Product,
  Reseller,
  POS,
  Daily,
  Weekly,
  Quarterly,
  RtmSort,
  SubRtmSort,
  UniqueKey,
} from "./constant";

function roundToOneDecimal(num) {
  if (isNullOrUndefinedOrEmptyString(num)) {
    return "-";
  }
  if (Number(num) <= 0) {
    return "0";
  }
  const result = _toFixed(num, 1);
  if (result === "0.0") {
    return "0";
  }
  return result;
}

function iPhoneThousandthsWithUnit(num) {
  if (isNullOrUndefinedOrEmptyString(num)) {
    return "-";
  }
  const _num = (() => {
    if ((num >= 0 && num < 1_000) || (num < 0 && num > -1_000)) {
      return _toFixed(num / 1_000, 1);
    }
    return Math.round(num / 1_000);
  })();
  const result = thousandths(_num);
  if (result === "0.0" || result === "-0.0") {
    return "0K";
  }
  if (result === "1.0") {
    return "1k";
  }
  if (result === "-1.0") {
    return "-1k";
  }
  return result + "K";
}

function otherThousandths(num) {
  if (isNullOrUndefinedOrEmptyString(num)) {
    return "-";
  }
  return thousandths(num);
}

function integerThousandths(num) {
  if (isNullOrUndefinedOrEmptyString(num)) {
    return "-";
  }
  const result = thousandths(Math.round(num));
  if (result === "0.0") {
    return "0";
  }
  return result;
}

function formatToPercentageInteger(value) {
  if (isNullOrUndefinedOrEmptyString(value)) {
    return "-";
  }
  const percentage = Math.round(value * 100);
  return percentage + "%";
}

export const formatSummaryData = (data = []) => {
  data.forEach((lobItem) => {
    (lobItem.details ?? []).forEach((item) => {
      item.in_stock = formatToPercentageInt(item.in_stock);
      item.woi = roundToOneDecimal(item.woi);
      if (item.lob === "iPhone") {
        item.inventory = iPhoneThousandthsWithUnit(item.inventory);
      } else {
        item.inventory = otherThousandths(item.inventory);
      }
      item.out_of_stock_point = integerThousandths(item.out_of_stock_point);
      const rate = formatToPercentageInt(item.out_of_stock_rate);
      item.out_of_stock_rate = rate;
      item.out_of_stock_color =
        rate?.endsWith("%") && item.lob === "iPhone"
          ? Number(rate?.slice(0, -1)) >= 10
            ? "attention"
            : ""
          : "";
    });
  });
  return data;
};

export const formatSummaryDetailColumns = (data = []) => {
  return data.map((value) => {
    const column = {
      label: value,
      prop: value,
    };
    if (value === "Total") {
      column.fixed = "left";
      column.width = 90;
      column["class-name"] = "col-total";
    } else if (value === "Sub-LOB") {
      column.fixed = "left";
      column.width = 226;
      column["class-name"] = "col-left";
    } else if (["Lifestyle", "Township", "Duty-free"].includes(value)) {
      column.width = 90;
    } else if (["Mono", "OTC", "CM", "CU", "CT", "CB"].includes(value)) {
      column.width = 80;
    } else {
      column.width = 120;
    }
    return column;
  });
};

export const formatSummaryDetailData = (data = [], is_iPhone) => {
  data.forEach((item) => {
    Object.keys(item).forEach((key) => {
      if (key !== "Sub-LOB") {
        item[key].origin_in_stock = item[key].in_stock;
        item[key].in_stock = formatToPercentageInt(item[key].in_stock);
        item[key].woi = roundToOneDecimal(item[key].woi);
        if (is_iPhone) {
          item[key].inventory = iPhoneThousandthsWithUnit(item[key].inventory);
        } else {
          item[key].inventory = otherThousandths(item[key].inventory);
        }
      }
    });
  });
  return data;
};

export const formatRolling7DaysRtmColumns = (data = []) => {
  return data.map((value, index) => {
    const column = {
      label: value,
      prop: value,
    };
    switch (value) {
      case "RTM":
        column.width = 202;
        break;
      case "Biz Type":
        column.width = 156;
        break;
      case "POS#":
        column.width = 102;
        break;
      case "In-stock POS#":
        column.width = 136;
        break;
      default:
        if (index !== data.length - 1) {
          column.width = 102;
        }
        break;
    }
    return column;
  });
};

export const NotInStockField = [
  "In-stock POS#",
  "POS#",
  "red_light",
  "yellow_light",
  "Biz Type", // 只 RTM view 有
  "RTM", // 只 RTM view 有
  "Sub-LOB", // 只 Product view 有
];

export const formatRolling7DaysRtmData = (data = []) => {
  const rtmJson = {};
  data.forEach((item) => {
    if (!rtmJson[item.RTM]) {
      rtmJson[item.RTM] = item.RTM;
    } else {
      item.RTM = "";
    }
    item["Biz Type"] = item["Biz Type"] ?? "-";
    item["POS#"] = integerThousandths(item["POS#"]);
    item["In-stock POS#"] = integerThousandths(item["In-stock POS#"]);
    Object.keys(item).forEach((key) => {
      if (!NotInStockField.includes(key)) {
        item[`origin_${key}`] = item[key];
        item[key] = formatToPercentageInteger(item[key]);
      }
    });
  });

  return data;
};

export const formatRolling7DaysProductColumns = (data = []) => {
  return data.map((value, index) => {
    const column = {
      label: value,
      prop: value,
    };
    switch (value) {
      case "Sub-LOB":
        column.width = 256;
        break;
      case "POS#":
        column.width = 116;
        break;
      case "In-stock POS#":
        column.width = 150;
        break;
      default:
        if (index !== data.length - 1) {
          column.width = 116;
        }
        break;
    }
    return column;
  });
};

export const formatRolling7DaysProductData = (data = []) => {
  data.forEach((item) => {
    item["POS#"] = integerThousandths(item["POS#"]);
    item["In-stock POS#"] = integerThousandths(item["In-stock POS#"]);
    Object.keys(item).forEach((key) => {
      if (!NotInStockField.includes(key)) {
        item[`origin_${key}`] = item[key];
        item[key] = formatToPercentageInteger(item[key]);
      }
    });
  });

  return data;
};

export const formatInventoryFlowColumns = (data = []) => {
  return data.map((value) => {
    const column = {
      label: value,
      prop: value,
    };
    if (value === "RTM") {
      column.width = 156;
    }
    if (value === "Biz Type") {
      column.width = 146;
    }
    if (value === "Sub-LOB") {
      column.width = 226;
    }
    if (value === "Open PO") {
      column.special = {
        icon: "diagnosis-inventory-open_po",
        className: "open-po-icon",
      };
    }
    if (value === "Factory(DN)") {
      column.special = {
        icon: "diagnosis-inventory-factory",
        className: "factory-icon",
      };
    }
    if (value === "Apple > Reseller") {
      column.special = {
        icon: "diagnosis-inventory-truck",
        className: "truck-icon",
        link: true,
        direction: "from",
      };
    }
    if (value === "Warehouse") {
      column.special = {
        icon: "diagnosis-inventory-warehouse",
        className: "warehouse-icon",
      };
    }
    if (value === "Reseller > POS") {
      column.special = {
        icon: "diagnosis-inventory-truck",
        className: "truck-icon",
        link: true,
        direction: "to",
      };
    }
    if (value === "POS") {
      column.special = {
        icon: "diagnosis-inventory-pos",
        className: "pos-icon",
      };
    }
    return column;
  });
};

export const formatInventoryFlowData = (data = [], is_iPhone) => {
  const rtmJson = {};
  data.forEach((item) => {
    if (!rtmJson[item.RTM]) {
      rtmJson[item.RTM] = item.RTM;
    } else {
      item.RTM = "";
    }
    item["Biz Type"] = item["Biz Type"] ?? "-";
    Object.keys(item).forEach((key) => {
      if (!["RTM", "Biz Type", "Sub-LOB"].includes(key)) {
        item[key].woi = roundToOneDecimal(item[key].woi);
        if (is_iPhone) {
          item[key].inventory = iPhoneThousandthsWithUnit(item[key].inventory);
        } else {
          item[key].inventory = integerThousandths(item[key].inventory);
        }
      }
    });
  });

  return data;
};

export const getNumberWithTwoDecimals = (value) => {
  if (value === null || value === undefined) {
    return value;
  }
  return Number(Number(value).toFixed(2));
};

const OutOfStockNotIncludeColumn = ["CB"];
const TableWidth = 1296;
export const formatOutOfStockColumns = (
  data = [],
  view = Product,
  time = Daily,
  isAdmin = true,
  lob = "iPhone"
) => {
  let fixed = 0;
  let lastFixedColumnIndex;
  const columns = data.reduce((prev, curr, index) => {
    if (
      OutOfStockNotIncludeColumn.includes(curr) ||
      ([Reseller, POS].includes(view) && curr === "RTM")
    ) {
      return prev;
    }

    const column = {
      label: curr,
      prop: curr,
    };

    if (curr === "QTD") {
      column.width = 96;
      column.fixed = "left";
    }
    if (curr.startsWith("FY")) {
      column.width = 116;
    }

    if (view === Product) {
      const isFixed = time === Quarterly && data.includes("QTD");
      switch (curr) {
        case "Sub-LOB":
          column.width = 178;
          if (isFixed) column.fixed = "left";
          break;
        case "Total":
          column.width = 82;
          column["class-name"] = "col-total";
          break;
        case "Lifestyle":
          column.width = 90;
          break;
        case "Mono":
        case "OTC":
          column.width = 84;
          break;
        case "CM":
        case "CU":
        case "CT":
          column.width = 80;
          break;
        case "Mass Merchant":
          column.width = 80;
          column.label = "MM";
          break;
        case "Township":
        case "Duty-free":
          column.width = 90;
          break;
        case "JD Self-run":
          column.width = 100;
          break;
        case "Online Others":
          column.width = 110;
          break;
        case "EDU":
          column.width = 74;
          break;
        default:
          break;
      }
    } else {
      const isFixed = time === Daily && ["iPad", "Mac"].includes(lob);
      switch (curr) {
        case "RTM":
          column.width = 156;
          if (isFixed || data.includes("QTD")) column.fixed = "left";
          break;
        case "Biz Type":
          column.width = 146;
          if (isFixed || data.includes("QTD")) column.fixed = "left";
          break;
        case "Reseller":
          column.width = 302;
          if (isFixed || data.includes("QTD")) column.fixed = "left";
          break;
        case "POS":
          column.width = 398;
          if (isFixed || data.includes("QTD")) column.fixed = "left";
          break;
        case "POS#":
          column.width = 96;
          if (isFixed) column.fixed = "left";
          break;
        case "Inv. Points#":
          column.width = 126;
          if (isFixed) column.fixed = "left";
          break;
        case "Out-of-stock":
          column.width = 130;
          if (isFixed) column.fixed = "left";
          break;
        case "Out-of-stock Points (# | %)":
          column.width = 200;
          break;
        case "Out-of-stock POS (# | %)":
          column.width = 188;
          break;
        case "Beyond 3 Days (# | %)":
          column.width = 178;
          break;
        case "Beyond 3 Days POS (# | %)":
          column.width = 200;
          break;
        default:
          if (lob === "iPhone") column.width = 108;
          else if (lob === "iPad")
            column.width = curr === "iPad (A16)" ? 106 : 146;
          else if (lob === "Mac") {
            if (curr === "iMac") column.width = 76;
            else if (curr === "Mac Mini") column.width = 92;
            else if (curr.includes("Air")) column.width = 146;
            else column.width = 176;
          } else if (lob === "Watch") {
            if (curr === "Series 10") column.width = 216;
            else if (curr === "Ultra 2") column.width = 150;
            else if (curr === "Watch SE (2nd Gen)") column.width = 276;
          }
          break;
      }
    }

    if (column?.fixed) {
      lastFixedColumnIndex = index;
      column.left = fixed;
      fixed += column.width - 1;
    }

    prev.push(column);
    return prev;
  }, []);

  if (typeof lastFixedColumnIndex === "number") {
    columns[lastFixedColumnIndex]["class-name"] = "col-last-fixed";
  } else {
    if (!(!isAdmin && view === Product))
      delete columns[columns.length - 1].width;
  }

  if (
    !isAdmin &&
    view === Product &&
    !columns.find((item) => item.label.includes("FY"))
  ) {
    const singleWidth = Math.floor(TableWidth / columns.length);
    return columns.map((item) => {
      return {
        ...item,
        width: singleWidth,
      };
    });
  }
  return columns;
};

export const formatDailyData = (
  data = [],
  isAdmin = true,
  view = Product,
  lob = "iPhone"
) => {
  const rtmJson = {};
  return data.map((item) =>
    Object.entries(item).reduce(
      (prev, [key, value]) => {
        if (key === "RTM") {
          if (isAdmin) {
            if (rtmJson[value]) {
              prev[key] = " ";
            } else {
              rtmJson[value] = value;
              prev[key] = value;
            }
          } else {
            prev[key] = value;
          }
        } else if (
          ["Sub-LOB", "Biz Type", "Reseller", "POS"].includes(key) ||
          key.startsWith("_")
        ) {
          prev[key] = value;
        } else if (["Inv. Points#", "POS#"].includes(key)) {
          prev[key] = integerThousandths(value);
        } else {
          const ratio = formatToPercentageInt(value?.ratio);
          prev[key] = {
            ratio,
            point: integerThousandths(value?.point),
            waive_tag: value?.waive_tag === "Y",
            color:
              ratio?.endsWith("%") && lob === "iPhone"
                ? Number(ratio?.slice(0, -1)) >= 10
                  ? "attention"
                  : ""
                : "",
          };
        }
        return prev;
      },
      {
        uniqueKey: UniqueKey[view].reduce((prev, curr) => {
          prev += item[curr] + "@";
          return prev;
        }, ""),
      }
    )
  );
};

export const formatWeeklyData = (
  data = [],
  isAdmin = true,
  view = Product,
  lob = "iPhone"
) => {
  const rtmJson = {};
  return data.map((item) =>
    Object.entries(item).reduce(
      (prev, [key, value]) => {
        if (key === "RTM") {
          if (isAdmin) {
            if (rtmJson[value]) {
              prev[key] = " ";
            } else {
              rtmJson[value] = value;
              prev[key] = value;
            }
          } else {
            prev[key] = value;
          }
        } else if (
          ["Sub-LOB", "Biz Type", "Reseller", "POS", "RTM"].includes(key) ||
          key.startsWith("_")
        ) {
          prev[key] = value;
        } else if (key === "waive_tag") {
          prev[key] = value === "Y";
        } else if (["Inv. Points#", "POS#"].includes(key)) {
          prev[key] = integerThousandths(value);
        } else if (key === "QTD") {
          prev[key] = formatToPercentageInt(value);
        } else {
          const ratio = formatToPercentageInt(value?.ratio);
          const resolveRatio = formatToPercentageInt(
            value?.["3d_unresolve_ratio"]
          );
          const point = integerThousandths(value?.point);

          if ("3d_unresolve_ratio" in value) {
            prev[key] = {
              value: ratio,
              resolve: resolveRatio,
              waive_tag: value?.waive_tag === "Y",
              color:
                lob === "iPhone" &&
                ratio?.endsWith("%") &&
                Number(ratio?.slice(0, -1)) >= 10 &&
                resolveRatio?.endsWith("%") &&
                Number(resolveRatio?.slice(0, -1)) >= 10
                  ? "attention"
                  : "",
            };
          } else {
            prev[key] = {
              value:
                "point" in value
                  ? ratio?.endsWith("%")
                    ? `${point} | ${ratio}`
                    : "-"
                  : ratio,
              waive_tag: value?.waive_tag === "Y",
              color:
                ratio?.endsWith("%") &&
                lob === "iPhone" &&
                Number(ratio?.slice(0, -1)) >= 10
                  ? "attention"
                  : "",
            };
          }
        }
        return prev;
      },
      {
        uniqueKey: UniqueKey[view].reduce((prev, curr) => {
          prev += item[curr] + "@";
          return prev;
        }, ""),
      }
    )
  );
};

export const formatQuarterlyData = (
  data = [],
  isAdmin = true,
  view = Product,
  lob = "iPhone"
) => {
  const rtmJson = {};
  return data.map((item, index) =>
    Object.entries(item).reduce(
      (prev, [key, value]) => {
        if (key === "RTM") {
          if (isAdmin) {
            if (rtmJson[value]) {
              prev[key] = " ";
            } else {
              rtmJson[value] = value;
              prev[key] = value;
            }
          } else {
            prev[key] = value;
          }
        } else if (
          ["Sub-LOB", "Biz Type", "Reseller", "POS", "RTM"].includes(key) ||
          key.startsWith("_")
        ) {
          prev[key] = value;
        } else if (key === "waive_tag") {
          prev[key] = value === "Y";
        } else if (["Inv. Points#", "POS#"].includes(key)) {
          prev[key] = integerThousandths(value);
        } else if (key === "QTD") {
          const qtd = formatToPercentageInt(value);
          prev[key] = {
            value: qtd,
            color:
              qtd?.endsWith("%") && lob === "iPhone"
                ? Number(qtd?.slice(0, -1)) >= 10
                  ? "attention"
                  : ""
                : "",
          };
        } else {
          const ratio = formatToPercentageInt(value?.ratio);
          const resolveRatio = formatToPercentageInt(value?.unresolve_ratio);
          const point = integerThousandths(value?.point);

          if ("unresolve_ratio" in value) {
            prev[key] = {
              value: ratio,
              resolve: resolveRatio,
              waive_tag: value?.waive_tag === "Y",
              color:
                lob === "iPhone" &&
                ratio?.endsWith("%") &&
                Number(ratio?.slice(0, -1)) >= 10 &&
                resolveRatio?.endsWith("%") &&
                Number(resolveRatio?.slice(0, -1)) >= 10
                  ? "attention"
                  : "",
            };
          } else {
            prev[key] = {
              value:
                "point" in value
                  ? ratio?.endsWith("%")
                    ? `${point} | ${ratio}`
                    : "-"
                  : ratio,
              waive_tag: value?.waive_tag === "Y",
              color:
                ratio?.endsWith("%") && lob === "iPhone"
                  ? Number(ratio?.slice(0, -1)) >= 10
                    ? "attention"
                    : ""
                  : "",
            };
          }
        }
        return prev;
      },
      {
        uniqueKey: UniqueKey[view].reduce((prev, curr) => {
          prev += item[curr] + "@";
          return prev;
        }, `${index}`),
      }
    )
  );
};

export const formatDateOptions = ({
  fiscal_quarters = [],
  fiscal_weeks = [],
}) => {
  return {
    [Quarterly]: fiscal_quarters.map((item) => {
      return {
        label: item,
        value: item,
      };
    }),
    [Weekly]: fiscal_weeks.map((item) => {
      return {
        label: item,
        value: item,
      };
    }),
  };
};

export const formatRtmOptions = (data = {}) => {
  let sort = 99;
  return Object.entries(data)
    .reduce((prev, [key, value]) => {
      prev.push({
        label: key,
        value: key,
        path: [key, "Total"],
        sort: RtmSort?.[key] ?? sort++,
        subrtm: Object.entries(value)
          .map(([vkey, vvalue]) => ({
            label: vkey,
            value: vkey === key ? `${vkey}|subrtm` : vkey,
            path: [key, vkey],
            sort: SubRtmSort?.[vkey] ?? sort++,
            reseller: Object.values(vvalue).map((vvvalue) => ({
              label: vvvalue?.reseller_name ?? "",
              value: `${vvvalue?.reseller_id ?? ""}@${
                vvvalue?.reseller_name ?? ""
              }@${vkey}`,
              pos: (vvvalue?.pos ?? []).map((pos) => ({
                label: pos?.pos_name ?? "",
                value: `${pos?.pos_id ?? ""}@${pos?.pos_name ?? ""}@${vkey}`,
              })),
            })),
          }))
          .sort((a, b) => a.sort - b.sort),
      });
      return prev;
    }, [])
    .sort((a, b) => a.sort - b.sort);
};

// v1.6
const CheckTableWidth = 918;
export const formatCheckPointData = (data = {}, rtm = "") => {
  const { col_name = [], row_data = [] } = data;
  const isAll = rtm === "All";
  const avgWidth = CheckTableWidth / (col_name.length + 1);
  const columns = [
    { label: "Inv. Points", prop: "sub_lob", width: isAll ? 126 : avgWidth },
    ...col_name.map((item) => {
      return {
        label: item,
        prop: item,
        width: isAll ? 182 : avgWidth,
      };
    }),
  ];

  // 计算单元格合并信息
  const spanArr = [];
  let pos = 0;

  row_data.forEach((_, index) => {
    if (index === 0) {
      spanArr.push(1);
      pos = 0;
    } else {
      // 比较当前行和前一行的 sub_lob 是否相同
      if (row_data[index].sub_lob === row_data[index - 1].sub_lob) {
        // 相同时，将之前存储的数据+1，当前位置设为0
        spanArr[pos] += 1;
        spanArr.push(0);
      } else {
        // 不同时，记录新的起始位置
        spanArr.push(1);
        pos = index;
      }
    }
  });

  const newData = row_data.map((item) => {
    return {
      ...item,
      uniqueKey: Object.values(item).reduce((prev, curr) => {
        prev += curr + "@";
        return prev;
      }, ""),
    };
  });

  return {
    columns,
    data: newData,
    spanArr,
  };
};
