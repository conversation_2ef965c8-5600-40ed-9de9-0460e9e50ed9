import { userService } from "@/services/UserService";
import { Platform, Program } from "./constants/common";
export const ProgramsPath = "/ai-program/programs";

export const AIProgram = {
  path: "/ai-program",
  name: Platform,
  meta: {
    name: Platform,
    display: true,
    permissionName: Platform,
  },
  redirect: () => {
    return { path: ProgramsPath };
  },
  condition: () => userService.hasPlatform(Platform),
  component: () => import("@/pages/AIProgram/indexView.vue"),
  children: [
    {
      path: ProgramsPath,
      name: "Programs",
      meta: {
        name: "Programs",
        display: true,
        permissionName: Program,
      },
      condition: () => userService.hasPlatform(Program),
      component: () => import("@/pages/AIProgram/Programs/indexView.vue"),
      redirect: () => {
        return { path: `${ProgramsPath}/list` };
      },
      children: [
        {
          path: `${ProgramsPath}/list`,
          name: "Programs List",
          meta: {
            name: "Programs List",
            parentPath: ProgramsPath,
            permissionName: Program,
          },
          condition: () => userService.hasPlatform(Program),
          component: () => import("@/pages/AIProgram/Programs/ListView.vue"),
        },
        {
          path: `${ProgramsPath}/detail`,
          name: "Programs Detail",
          meta: {
            name: "Programs Detail",
            parentPath: ProgramsPath,
            permissionName: Program,
          },
          condition: () => userService.hasPlatform(Program),
          component: () => import("@/pages/AIProgram/Programs/DetailView.vue"),
        },
      ],
    },
  ],
};
