// 简化后的 axios 配置 - 移除不必要的复杂度
import { clone } from "lodash-es";
import { VAxios } from "./Axios";
import { RequestEnum, ContentTypeEnum } from "./httpEnum";
import { isString, deepMerge } from "./utils";
import qs from "qs";
import { joinTimestamp, formatRequestDate, formatRestful } from "./helper";
import { AxiosRetry } from "./axiosRetry";
import axios from "axios";

/**
 * 简化后的请求前置钩子 - 移除不必要的 URL 处理
 */
const beforeRequestHook = (config, options) => {
  const { formatDate, joinTime = true } = options;
  const params = config.params || {};
  const data = config.data || false;

  // 格式化日期数据
  if (formatDate && data && !isString(data)) {
    formatRequestDate(data);
  }

  if (config.method?.toUpperCase() === RequestEnum.GET) {
    handleGetRequest(config, params, joinTime);
  } else {
    handleNonGetRequest(config, params, data, formatDate);
  }

  return config;
};

/**
 * 处理 GET 请求
 */
function handleGetRequest(config, params, joinTime) {
  if (isString(params)) {
    // RESTful 风格
    formatRestful(config, params, joinTime);
  } else {
    // 标准查询参数
    config.params = Object.assign(params || {}, joinTimestamp(joinTime, false));
    // 采用qs处理get请求参数，解决axios get请求参数数组问题
    config.paramsSerializer = (params) =>
      qs.stringify(params, { arrayFormat: "repeat" });
  }
}

/**
 * 处理非 GET 请求
 */
function handleNonGetRequest(config, params, data, formatDate) {
  if (isString(params)) {
    // RESTful 风格
    formatRestful(config, params, false);
  } else {
    // 标准请求
    if (formatDate) {
      formatRequestDate(params);
    }

    config.data = data;
    config.params = params;
  }
}

/**
 * @description: 数据处理配置
 */
const transform = {
  /**
   * @description: 处理响应数据
   */
  transformResponseHook: (res, options) => {
    const { request = {} } = res;
    const { isTransformResponse, isReturnNativeResponse } = options;

    if (isReturnNativeResponse) {
      return res;
    }

    if (!isTransformResponse) {
      return res.data;
    }

    return request?.responseType === "blob" ? res : res.data;
  },

  // 使用简化后的请求前置钩子
  beforeRequestHook,

  /**
   * @description: 请求拦截器处理
   */
  requestInterceptors: (config, options) => {
    const token = localStorage.getItem("token") || null;
    if (token && config?.requestOptions?.withToken !== false) {
      config.headers.Authorization = options.authenticationScheme
        ? `${options.authenticationScheme} ${token}`
        : token;
    }
    return config;
  },

  /**
   * @description: 响应拦截器处理
   */
  responseInterceptors: (res) => {
    return res;
  },

  /**
   * @description: 响应错误处理
   */
  responseInterceptorsCatch: (axiosInstance, error) => {
    const { response, code, message, config } = error || {};

    if (axios.isCancel(error)) {
      return Promise.reject(error);
    }

    // 统一错误处理
    let errMessage = response?.data?.message || "";
    const err = error?.toString?.() || "";

    try {
      if (code === "ECONNABORTED" && message.indexOf("timeout") !== -1) {
        errMessage = "Request timeout, please try again later.";
      }
      if (err?.includes("Network Error")) {
        errMessage = `Network Error: ${err}`;
      }

      if (errMessage) {
        console.error("Request Error:", errMessage);
      }
    } catch (error) {
      console.error("Error processing failed request:", error);
    }

    // 自动重试机制（仅 GET 请求）
    const retryOptions = config?.requestOptions?.retryRequest;
    if (
      config?.method?.toUpperCase() === RequestEnum.GET &&
      retryOptions?.isOpenRetry
    ) {
      const retryRequest = new AxiosRetry();
      retryRequest.retry(axiosInstance, error);
    }

    return Promise.reject(error);
  },
};

/**
 * 创建 axios 实例
 */
function createAxios(opt = {}) {
  return new VAxios(
    deepMerge(
      {
        // 认证方案
        authenticationScheme: "Bearer",
        timeout: 60 * 1000,

        // 使用 axios 原生的 baseURL，不需要额外的 URL 处理
        baseURL: opt.baseURL || "",

        headers: { "Content-Type": ContentTypeEnum.JSON },
        transform: clone(transform),

        // 简化后的配置项
        requestOptions: {
          // 是否返回原生响应头
          isReturnNativeResponse: false,
          // 需要对返回数据进行处理
          isTransformResponse: true,
          // 格式化提交参数时间
          formatDate: true,
          // 消息提示类型
          errorMessageMode: "message",
          // 是否加入时间戳
          joinTime: true,
          // 忽略重复请求
          ignoreCancelToken: true,
          // 是否携带token
          withToken: true,
          // 重试配置
          retryRequest: {
            isOpenRetry: false,
            count: 5,
            waitTime: 100,
          },
        },
      },
      opt || {}
    )
  );
}

// 创建默认实例
const http = createAxios();

export { createAxios, http };

// 使用示例：
//
// // 基础使用
// const http = createAxios({
//   baseURL: 'https://api.example.com'  // 直接用 axios 原生的 baseURL
// });
//
// // 多个 API 服务
// export const userApi = createAxios({
//   baseURL: 'https://user-api.example.com'
// });
//
// export const orderApi = createAxios({
//   baseURL: 'https://order-api.example.com'
// });
