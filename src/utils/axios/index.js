// axios配置  可自行根据项目进行更改，只需更改该文件即可，其他文件可以不动
import { clone } from "lodash-es";
import { VAxios } from "./Axios";
import { RequestEnum, ContentTypeEnum } from "./httpEnum";
import { isString, deepMerge } from "./utils";
import qs from "qs";
import { joinTimestamp, formatRequestDate, formatRestful } from "./helper";
import { AxiosRetry } from "./axiosRetry";
import axios from "axios";

/**
 * @description: 数据处理，方便区分多种处理方式
 */
const transform = {
  /**
   * @description: 处理响应数据。如果数据不是预期格式，可直接抛出错误
   */
  transformResponseHook: (res, options) => {
    const { request = {} } = res;
    const { isTransformResponse, isReturnNativeResponse } = options;

    // 是否返回原生响应头 比如：需要获取响应头时使用该属性
    if (isReturnNativeResponse) {
      return res;
    }
    // 不进行任何处理，直接返回
    // 用于页面代码可能需要直接获取code，data，message这些信息时开启
    if (!isTransformResponse) {
      return res.data;
    }

    return request?.responseType === "blob" ? res : res.data;
  },

  // 请求之前处理config
  beforeRequestHook: (config, options) => {
    const { formatDate, joinTime = true } = options;

    const params = config.params || {};
    const data = config.data || false;
    formatDate && data && !isString(data) && formatRequestDate(data);
    if (config.method?.toUpperCase() === RequestEnum.GET) {
      if (!isString(params)) {
        // 给 get 请求加上时间戳参数，避免从缓存中拿数据。
        config.params = Object.assign(
          params || {},
          joinTimestamp(joinTime, false)
        );
        // 采用qs处理get请求参数，解决axios get请求参数数组问题
        config.paramsSerializer = (params) =>
          qs.stringify(params, { arrayFormat: "repeat" });
      } else {
        // 兼容restful风格
        formatRestful(config, params, joinTime);
      }
    } else {
      if (!isString(params)) {
        formatDate && formatRequestDate(params);
        config.data = data;
      } else {
        // 兼容restful风格
        formatRestful(config, params, joinTime);
      }
    }
    return config;
  },

  /**
   * @description: 请求拦截器处理
   */
  requestInterceptors: (config, options) => {
    // 请求之前处理config
    const token = localStorage.getItem("token");
    if (token && config?.requestOptions?.withToken !== false) {
      // jwt token
      config.headers.Authorization = options.authenticationScheme
        ? `${options.authenticationScheme} ${token}`
        : token;
    }
    return config;
  },

  /**
   * @description: 响应拦截器处理
   */
  responseInterceptors: (res) => {
    return res;
  },

  /**
   * @description: 响应错误处理
   */
  responseInterceptorsCatch: (axiosInstance, error) => {
    const { response, code, message, config } = error || {};

    const msg = response?.data?.message ?? "";
    const err = error?.toString?.() ?? "";
    let errMessage = msg;

    if (axios.isCancel(error)) {
      return Promise.reject(error);
    }

    try {
      if (code === "ECONNABORTED" && message.indexOf("timeout") !== -1) {
        errMessage = "Request timeout, please try again later.";
      }
      if (err?.includes("Network Error")) {
        errMessage = `Network Error: ${err}`;
      }

      if (errMessage) {
        return Promise.reject(error);
      }
    } catch (error) {
      throw new Error(error);
    }

    // 添加自动重试机制 保险起见 只针对GET请求
    const retryRequest = new AxiosRetry();
    const { isOpenRetry } = config?.requestOptions?.retryRequest;
    config.method?.toUpperCase() === RequestEnum.GET &&
      isOpenRetry &&
      retryRequest.retry(axiosInstance, error);
    return Promise.reject(error);
  },
};

function createAxios(opt) {
  return new VAxios(
    // 深度合并
    deepMerge(
      {
        // See https://developer.mozilla.org/en-US/docs/Web/HTTP/Authentication#authentication_schemes
        // authentication schemes，e.g: Bearer
        authenticationScheme: "Bearer",
        // authenticationScheme: "",
        timeout: 60 * 1000,
        // 基础接口地址
        baseURL: "",

        headers: { "Content-Type": ContentTypeEnum.JSON },
        // 如果是form-data格式
        // headers: { 'Content-Type': ContentTypeEnum.FORM_URLENCODED },
        // 数据处理方式
        transform: clone(transform),
        // 配置项，下面的选项都可以在独立的接口请求中覆盖
        requestOptions: {
          // 默认将prefix 添加到url
          joinPrefix: true,
          // 是否返回原生响应头 比如：需要获取响应头时使用该属性
          isReturnNativeResponse: false,
          // 需要对返回数据进行处理
          isTransformResponse: true,
          // post请求的时候添加参数到url
          joinParamsToUrl: false,
          // 格式化提交参数时间
          formatDate: true,
          // 消息提示类型
          errorMessageMode: "message",
          // 接口地址
          apiUrl: "",
          // 接口拼接地址
          urlPrefix: "",
          //  是否加入时间戳
          joinTime: true,
          // 忽略重复请求
          ignoreCancelToken: true,
          // 是否携带token
          withToken: false,
          retryRequest: {
            isOpenRetry: false,
            count: 5,
            waitTime: 100,
          },
          otherCode: [],
        },
      },
      opt || {}
    )
  );
}
const http = createAxios();
export { createAxios, http };

// other api url
// export const otherHttp = createAxios({
//   requestOptions: {
//     apiUrl: 'xxx',
//     urlPrefix: 'xxx',
//   },
// });

// export function testRetry() {
//   return defHttp.get(
//     { url: Api.TestRetry },
//     {
//       retryRequest: {
//         isOpenRetry: true,
//         count: 5,
//         waitTime: 1000,
//       },
//     },
//   );
// }

// cancel
// const config = getPendingUrl(config);
// axiosCanceler = new AxiosCanceler();
// axiosCanceler?.removePending(config);

//  transform.transformResponseHook
//  自行处理数据 code判断等
// const transform.transformResponseHook = (res, options) => {
//   return res.data;
// }
// export const otherHttp = createAxios({
// transform
// });
