import * as Sentry from "@sentry/vue";
import router from "@/routerV2";
import { ResultEnum } from "./httpEnum";

export function reportCodeLog(res) {
  if (res.status !== 200) {
    reportStatusLog(res);
    return;
  }
  const { config, data } = res;
  const { code = 0 } = data;
  if (code !== ResultEnum.SUCCESS) {
    const { name, permissionName } = router.currentRoute?.meta;
    Sentry.captureMessage("Expert Http Code Error", {
      tags: {
        error_module: permissionName || name,
      },
      contexts: {
        config,
        data,
        route: router.currentRoute,
        time: Date.now(),
      },
      level: Sentry.Severity.Info,
    });
  }
}

export function reportStatusLog(err) {
  const { name, permissionName } = router.currentRoute?.meta;
  Sentry.captureMessage("Expert Http Status Error", {
    tags: {
      error_module: permissionName || name,
    },
    contexts: {
      err,
      route: router.currentRoute,
      time: Date.now(),
    },
    level: Sentry.Severity.Info,
  });
}
