import axios from "axios";
import qs from "qs";
import { AxiosCanceler } from "./axiosCancel";
import { isFunction, isObject } from "./utils";
import { cloneDeep } from "lodash-es";
import { ContentTypeEnum, RequestEnum } from "./httpEnum";
import { reportCodeLog, reportStatusLog } from "./sentryLog";

/**
 * @description: 优化后的 axios module
 */
export class VAxios {
  axiosInstance;
  options;

  constructor(options) {
    this.options = options;
    this.axiosInstance = axios.create(options);
    this.setupInterceptors();
  }

  /**
   * @description: Create axios instance
   */
  createAxios(config) {
    this.axiosInstance = axios.create(config);
  }

  getTransform() {
    const { transform } = this.options;
    return transform;
  }

  getAxios() {
    return this.axiosInstance;
  }

  /**
   * @description: Reconfigure axios
   */
  configAxios(config) {
    if (!this.axiosInstance) {
      return;
    }
    this.createAxios(config);
  }

  /**
   * @description: Set general header
   */
  setHeader(headers) {
    if (!this.axiosInstance) {
      return;
    }
    Object.assign(this.axiosInstance.defaults.headers, headers);
  }

  /**
   * @description: Interceptor configuration 拦截器配置
   */
  setupInterceptors() {
    const {
      axiosInstance,
      options: { transform },
    } = this;

    if (!transform) {
      return;
    }

    const {
      requestInterceptors,
      requestInterceptorsCatch,
      responseInterceptors,
      responseInterceptorsCatch,
    } = transform;

    const axiosCanceler = new AxiosCanceler();

    // Request interceptor configuration processing
    this.axiosInstance.interceptors.request.use((config) => {
      // If cancel repeat request is turned on, then cancel repeat request is prohibited
      const { requestOptions } = this.options;
      const ignoreCancelToken = requestOptions?.ignoreCancelToken ?? true;

      !ignoreCancelToken && axiosCanceler.addPending(config);

      if (requestInterceptors && isFunction(requestInterceptors)) {
        config = requestInterceptors(config, this.options);
      }
      return config;
    }, undefined);

    // Request interceptor error capture
    requestInterceptorsCatch &&
      isFunction(requestInterceptorsCatch) &&
      this.axiosInstance.interceptors.request.use(
        undefined,
        requestInterceptorsCatch
      );

    // Response result interceptor processing
    this.axiosInstance.interceptors.response.use((res) => {
      reportCodeLog(res);
      res && axiosCanceler.removePending(res.config);
      if (responseInterceptors && isFunction(responseInterceptors)) {
        res = responseInterceptors(res);
      }
      return res;
    }, undefined);

    // Response result interceptor error capture
    responseInterceptorsCatch &&
      isFunction(responseInterceptorsCatch) &&
      this.axiosInstance.interceptors.response.use(undefined, (error) => {
        reportStatusLog(error);
        return responseInterceptorsCatch(this.axiosInstance, error);
      });
  }

  /**
   * @description: File Upload
   */
  uploadFile(config, params) {
    const formData = new window.FormData();
    const customFilename = params.name || "file";

    if (params.filename) {
      formData.append(customFilename, params.file, params.filename);
    } else {
      formData.append(customFilename, params.file);
    }

    if (params.data) {
      Object.keys(params.data).forEach((key) => {
        const value = params.data[key];
        if (Array.isArray(value)) {
          value.forEach((item) => {
            formData.append(`${key}[]`, item);
          });
          return;
        }

        formData.append(key, params.data[key]);
      });
    }

    return this.axiosInstance.request({
      ...config,
      method: "POST",
      data: formData,
      headers: {
        "Content-type": ContentTypeEnum.FORM_DATA,
        ignoreCancelToken: true,
      },
    });
  }

  // support form-data
  supportFormData(config) {
    const headers = config.headers || this.options.headers;
    const contentType = headers?.["Content-Type"] || headers?.["content-type"];

    if (
      contentType !== ContentTypeEnum.FORM_URLENCODED ||
      !Reflect.has(config, "data") ||
      config.method?.toUpperCase() === RequestEnum.GET
    ) {
      return config;
    }

    return {
      ...config,
      data: qs.stringify(config.data, { arrayFormat: "brackets" }),
    };
  }

  get(url = "", config = {}, options) {
    return this.request({ url, ...config, method: "GET" }, options);
  }

  post(url = "", data, config = {}, options) {
    return this.request({ url, data, ...config, method: "POST" }, options);
  }

  put(url = "", data, config = {}, options) {
    return this.request({ url, data, ...config, method: "PUT" }, options);
  }

  delete(url = "", config = {}, options) {
    return this.request({ url, ...config, method: "DELETE" }, options);
  }

  patch(url = "", data, config = {}, options) {
    return this.request({ url, data, ...config, method: "PATCH" }, options);
  }

  /**
   * 优化后的请求方法
   */
  request(config, options) {
    // 基本类型检查
    if (!config || typeof config !== "object") {
      return Promise.reject(new Error("Invalid config object"));
    }

    // 优化深拷贝 - 只拷贝必要的属性
    let conf = this.optimizedClone(config);

    const transform = this.getTransform();
    const { requestOptions } = this.options;
    const opt = Object.assign({}, requestOptions, options);

    const { beforeRequestHook, requestCatchHook, transformResponseHook } =
      transform || {};

    if (beforeRequestHook && isFunction(beforeRequestHook)) {
      conf = beforeRequestHook(conf, opt);
    }

    conf.requestOptions = opt;
    conf = this.supportFormData(conf);

    return new Promise((resolve, reject) => {
      this.axiosInstance
        .request(conf)
        .then((res) => {
          if (transformResponseHook && isFunction(transformResponseHook)) {
            try {
              const ret = transformResponseHook(res, opt);
              resolve(ret);
            } catch (err) {
              reject(err || new Error("request error!"));
            }
            return;
          }
          resolve(res);
        })
        .catch((e) => {
          if (requestCatchHook && isFunction(requestCatchHook)) {
            reject(requestCatchHook(e, opt));
            return;
          }
          if (axios.isAxiosError(e)) {
            reportStatusLog(e);
          }
          reject(e);
        });
    });
  }

  /**
   * 优化的克隆方法 - 避免深拷贝大对象
   */
  optimizedClone(config) {
    // 需要特殊处理的属性
    const specialProps = [
      "cancelToken",
      "onUploadProgress",
      "onDownloadProgress",
    ];
    const cloned = {};

    // 浅拷贝大部分属性
    for (const key in config) {
      if (config.hasOwnProperty(key)) {
        if (specialProps.includes(key)) {
          // 特殊属性直接引用，不拷贝
          cloned[key] = config[key];
        } else if (key === "data" && this.shouldDeepCloneData(config.data)) {
          // 只有在必要时才深拷贝 data
          cloned[key] = cloneDeep(config[key]);
        } else if (
          isObject(config[key]) &&
          !this.isSpecialObject(config[key])
        ) {
          // 普通对象进行浅拷贝
          cloned[key] = { ...config[key] };
        } else {
          // 其他情况直接赋值
          cloned[key] = config[key];
        }
      }
    }

    return cloned;
  }

  /**
   * 判断是否需要深拷贝 data
   */
  shouldDeepCloneData(data) {
    return (
      data &&
      isObject(data) &&
      !(data instanceof FormData) &&
      !(data instanceof File) &&
      !(data instanceof Blob)
    );
  }

  /**
   * 判断是否为特殊对象（不应该被拷贝）
   */
  isSpecialObject(obj) {
    return (
      obj instanceof FormData ||
      obj instanceof File ||
      obj instanceof Blob ||
      obj instanceof ArrayBuffer ||
      obj instanceof Date ||
      obj instanceof RegExp
    );
  }
}
