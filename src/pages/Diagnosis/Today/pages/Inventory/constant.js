export const draftSummary = [
  { lob: "iPhone", details: [] },
  { lob: "iPad", details: [] },
  { lob: "Mac", details: [] },
  { lob: "Watch", details: [] },
];

export const OutOfStock = "out_of_stock";
export const InStock = "in_stock";
export const WOI = "woi";
export const Inventory = "inventory";

export const SummaryColumns = [
  { label: "Sub-LOB", prop: "sub_lob", width: 180, "class-name": "col-lob" },
  { label: "Sub-LOB In-stock%", prop: InStock, width: 138 },
  { label: "WOI", prop: WOI, width: 62 },
  { label: "Inventory", prop: Inventory, width: 68 },
  {
    label: "Out-of-stock Points",
    prop: `${OutOfStock}_point`,
    "class-name": "col-right",
  },
];

export const tabsList = [
  { label: "iPhone", value: "iPhone" },
  { label: "iPad", value: "iPad" },
  { label: "Mac", value: "Mac" },
  { label: "Watch", value: "Watch" },
];

export const supplyOptions = [
  { label: "In-stock%", value: InStock },
  { label: "WOI", value: WOI },
  { label: "Inventory", value: Inventory },
];

export const Product = "product";
export const RTM = "rtm";
export const Reseller = "reseller";
export const POS = "pos";

export const operationalOptions = [
  { label: "RTM View", value: RTM },
  { label: "Product View", value: Product },
];

export const PerformanceOptions = [
  { label: "Out-of-stock Points%", value: OutOfStock },
  { label: "Sub-LOB In-stock%", value: InStock },
  { label: "WOI", value: WOI },
  { label: "Inventory", value: Inventory },
];

export const Daily = "daily";
export const Weekly = "weekly";
export const Quarterly = "quarterly";

export const TimeOptions = [
  { label: "Daily", value: Daily },
  { label: "Weekly", value: Weekly },
  { label: "Quarterly", value: Quarterly },
];

export const OutOfStockTabs = [
  { label: "Product View", value: Product, isAdmin: "All" },
  { label: "RTM View", value: RTM, isAdmin: true },
  { label: "Reseller View", value: Reseller, isAdmin: "All" },
  { label: "POS View", value: POS, isAdmin: false },
];

export const NumberTypeOptions = [
  { label: "%", value: "ratio" },
  { label: "#", value: "point" },
];

export const RtmSort = {
  "Mono Brand": 1,
  "Multi Brand": 2,
  Carrier: 3,
  Online: 4,
  "Channel Online": 5,
  EDU: 6,
};

export const SubRtmSort = {
  Mono: 1,
  Lifestyle: 2,
  OTC: 3,
  Township: 4,
  "Mass Merchant": 5,
  "Duty-free": 6,
  CM: 7,
  CU: 8,
  CT: 9,
  CB: 10,
  "JD Self-run": 11,
  "Online Others": 12,
  EDU: 13,
};

export const Role2Rtm = {
  "Mono User": "Mono Brand",
  "Multi User": "Multi Brand",
  "Carrier User": "Carrier",
  "Online User": "Channel Online",
  "EDU User": "EDU",
};

export const UniqueKey = {
  [Product]: ["Sub-LOB"],
  [RTM]: ["RTM", "Biz Type"],
  [Reseller]: ["_rtm", "_biz_type", "_reseller_id", "_reseller_name"],
  [POS]: ["RTM", "_biz_type", "_pos_id", "_pos_name"],
};

export const TypeOptions = [
  { label: "Out-of-stock%", value: "value" },
  { label: "Beyond 3 Days%", value: "resolve" },
];

export const DrawViewOptions = [
  { label: "Check Points", value: "check" },
  { label: "Waive Points", value: "waive", disabled: true },
];
